{"name": "@peoplenest/backend", "version": "1.0.0", "description": "PeopleNest HRMS Backend Services", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "typeorm migration:generate -d src/config/typeorm.config.ts", "migration:create": "typeorm migration:create", "migration:run": "typeorm migration:run -d src/config/typeorm.config.ts", "migration:revert": "typeorm migration:revert -d src/config/typeorm.config.ts", "schema:drop": "typeorm schema:drop -d src/config/typeorm.config.ts", "seed:run": "ts-node src/database/seeds/run-seeds.ts", "admin:create": "ts-node src/scripts/create-admin.ts", "admin:list": "ts-node src/scripts/list-admins.ts", "admin:password": "ts-node src/scripts/reset-password.ts", "admin:activate": "ts-node src/scripts/activate-user.ts", "admin:deactivate": "ts-node src/scripts/deactivate-user.ts"}, "dependencies": {"@apollo/gateway": "^2.11.2", "@nestjs/apollo": "^12.0.11", "@nestjs/axios": "^3.1.3", "@nestjs/bull": "^10.0.1", "@nestjs/cache-manager": "^2.1.1", "@nestjs/common": "^10.2.10", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.2.10", "@nestjs/cqrs": "^10.2.6", "@nestjs/event-emitter": "^2.0.3", "@nestjs/graphql": "^12.0.11", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.2.10", "@nestjs/mongoose": "^10.0.2", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.2.10", "@nestjs/platform-fastify": "^10.2.10", "@nestjs/platform-socket.io": "^10.4.19", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.1.16", "@nestjs/terminus": "^10.2.0", "@nestjs/throttler": "^5.0.1", "@nestjs/typeorm": "^10.0.1", "@nestjs/websockets": "^10.2.10", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "apollo-server-express": "^3.12.1", "bcrypt": "^5.1.1", "bull": "^4.12.0", "cache-manager": "^5.3.2", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "crypto-js": "^4.2.0", "express-rate-limit": "^7.1.5", "graphql": "^16.8.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "kafkajs": "^2.2.4", "mongoose": "^8.0.3", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "qrcode": "^1.5.4", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "swagger-ui-express": "^5.0.0", "twilio": "^4.19.0", "typeorm": "^0.3.17", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.10", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/crypto-js": "^4.2.1", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/nodemailer": "^6.4.14", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/pg": "^8.10.9", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coverageThreshold": {"global": {"branches": 85, "functions": 85, "lines": 85, "statements": 85}}}, "optionalDependencies": {"@apollo/subgraph": "^2.11.2", "@grpc/grpc-js": "^1.13.4", "@grpc/proto-loader": "^0.7.15", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.8", "aws-sdk": "^2.1692.0", "mock-aws-s3": "^4.0.2", "mqtt": "^5.13.1", "nats": "^2.29.3", "nock": "^14.0.5", "ts-morph": "^26.0.0"}}