import { Controller, Get, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';
import { Public } from '@app/common';

@ApiTags('Application')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Public()
  @Get()
  @ApiOperation({ 
    summary: 'Get application information',
    description: 'Returns basic information about the PeopleNest HRMS API'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Application information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        version: { type: 'string' },
        description: { type: 'string' },
        environment: { type: 'string' },
        timestamp: { type: 'string' },
        uptime: { type: 'number' },
        endpoints: {
          type: 'object',
          properties: {
            health: { type: 'string' },
            docs: { type: 'string' },
            api: { type: 'string' },
          }
        }
      }
    }
  })
  getInfo() {
    return this.appService.getApplicationInfo();
  }

  @Public()
  @Get('version')
  @ApiOperation({ 
    summary: 'Get application version',
    description: 'Returns the current version of the API'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Version information retrieved successfully'
  })
  getVersion() {
    return this.appService.getVersion();
  }

  @Public()
  @Post('test')
  @ApiOperation({
    summary: 'Test POST endpoint',
    description: 'Simple test endpoint for debugging POST requests'
  })
  @ApiResponse({
    status: 200,
    description: 'Test successful'
  })
  testPost(@Body() body: any): object {
    return {
      message: 'Test POST successful',
      receivedBody: body,
      timestamp: new Date().toISOString()
    };
  }
}
