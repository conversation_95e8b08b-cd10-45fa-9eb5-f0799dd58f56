export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
  ARCHIVED = 'archived',
}

export enum EmployeeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ON_LEAVE = 'on_leave',
  TERMINATED = 'terminated',
  RETIRED = 'retired',
  SUSPENDED = 'suspended',
}

export enum EmploymentType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  CONTRACT = 'contract',
  TEMPORARY = 'temporary',
  INTERN = 'intern',
  CONSULTANT = 'consultant',
}

export enum EmploymentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  TERMINATED = 'terminated',
  ON_LEAVE = 'on_leave',
  SUSPENDED = 'suspended',
  PROBATION = 'probation',
  NOTICE_PERIOD = 'notice_period',
}

export enum PayrollStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  PROCESSED = 'processed',
  PAID = 'paid',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
  REVERSED = 'reversed',
}

export enum LeaveStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

export enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LATE = 'late',
  HALF_DAY = 'half_day',
  ON_LEAVE = 'on_leave',
  HOLIDAY = 'holiday',
}

export enum DocumentType {
  RESUME = 'resume',
  CONTRACT = 'contract',
  ID_DOCUMENT = 'id_document',
  CERTIFICATE = 'certificate',
  POLICY = 'policy',
  HANDBOOK = 'handbook',
  FORM = 'form',
  REPORT = 'report',
  OTHER = 'other',
}

export enum NotificationType {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app',
  SLACK = 'slack',
  TEAMS = 'teams',
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum AuditAction {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  LOGIN = 'login',
  LOGOUT = 'logout',
  EXPORT = 'export',
  IMPORT = 'import',
  APPROVE = 'approve',
  REJECT = 'reject',
  SUBMIT = 'submit',
  CANCEL = 'cancel',
}

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
  PREFER_NOT_TO_SAY = 'prefer_not_to_say',
}

export enum MaritalStatus {
  SINGLE = 'single',
  MARRIED = 'married',
  DIVORCED = 'divorced',
  WIDOWED = 'widowed',
  SEPARATED = 'separated',
  OTHER = 'other',
}

export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  CAD = 'CAD',
  AUD = 'AUD',
  JPY = 'JPY',
  INR = 'INR',
  CNY = 'CNY',
  BRL = 'BRL',
  MXN = 'MXN',
}

export enum Country {
  US = 'US',
  CA = 'CA',
  GB = 'GB',
  DE = 'DE',
  FR = 'FR',
  AU = 'AU',
  JP = 'JP',
  IN = 'IN',
  CN = 'CN',
  BR = 'BR',
  MX = 'MX',
}

export enum TimeZone {
  UTC = 'UTC',
  EST = 'America/New_York',
  PST = 'America/Los_Angeles',
  CST = 'America/Chicago',
  MST = 'America/Denver',
  GMT = 'Europe/London',
  CET = 'Europe/Berlin',
  JST = 'Asia/Tokyo',
  IST = 'Asia/Kolkata',
  AEST = 'Australia/Sydney',
}

export enum PayrollFrequency {
  WEEKLY = 'weekly',
  BI_WEEKLY = 'bi_weekly',
  SEMI_MONTHLY = 'semi_monthly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  ANNUALLY = 'annually',
}

export enum PayrollItemType {
  SALARY = 'salary',
  HOURLY = 'hourly',
  OVERTIME = 'overtime',
  BONUS = 'bonus',
  COMMISSION = 'commission',
  ALLOWANCE = 'allowance',
  REIMBURSEMENT = 'reimbursement',
  DEDUCTION = 'deduction',
  TAX = 'tax',
  BENEFIT = 'benefit',
  CONTRIBUTION = 'contribution',
}

export enum TaxType {
  FEDERAL_INCOME = 'federal_income',
  STATE_INCOME = 'state_income',
  LOCAL_INCOME = 'local_income',
  SOCIAL_SECURITY = 'social_security',
  MEDICARE = 'medicare',
  UNEMPLOYMENT = 'unemployment',
  DISABILITY = 'disability',
  WORKERS_COMP = 'workers_comp',
  OTHER = 'other',
}

export enum FilingStatus {
  SINGLE = 'single',
  MARRIED_FILING_JOINTLY = 'married_filing_jointly',
  MARRIED_FILING_SEPARATELY = 'married_filing_separately',
  HEAD_OF_HOUSEHOLD = 'head_of_household',
  QUALIFYING_WIDOW = 'qualifying_widow',
}

export enum DeductionType {
  HEALTH_INSURANCE = 'health_insurance',
  DENTAL_INSURANCE = 'dental_insurance',
  VISION_INSURANCE = 'vision_insurance',
  LIFE_INSURANCE = 'life_insurance',
  RETIREMENT_401K = 'retirement_401k',
  RETIREMENT_PENSION = 'retirement_pension',
  HSA = 'hsa',
  FSA = 'fsa',
  PARKING = 'parking',
  TRANSIT = 'transit',
  UNION_DUES = 'union_dues',
  GARNISHMENT = 'garnishment',
  LOAN_REPAYMENT = 'loan_repayment',
  OTHER = 'other',
}

export enum BenefitType {
  HEALTH_INSURANCE = 'health_insurance',
  DENTAL_INSURANCE = 'dental_insurance',
  VISION_INSURANCE = 'vision_insurance',
  LIFE_INSURANCE = 'life_insurance',
  DISABILITY_INSURANCE = 'disability_insurance',
  RETIREMENT_401K = 'retirement_401k',
  RETIREMENT_PENSION = 'retirement_pension',
  HSA = 'hsa',
  FSA = 'fsa',
  PTO = 'pto',
  SICK_LEAVE = 'sick_leave',
  MATERNITY_LEAVE = 'maternity_leave',
  PATERNITY_LEAVE = 'paternity_leave',
  EDUCATION_ASSISTANCE = 'education_assistance',
  GYM_MEMBERSHIP = 'gym_membership',
  COMMUTER_BENEFITS = 'commuter_benefits',
  OTHER = 'other',
}

export enum PaymentMethod {
  DIRECT_DEPOSIT = 'direct_deposit',
  CHECK = 'check',
  CASH = 'cash',
  WIRE_TRANSFER = 'wire_transfer',
  PAYROLL_CARD = 'payroll_card',
  CRYPTOCURRENCY = 'cryptocurrency',
}

export enum PayrollRunStatus {
  DRAFT = 'draft',
  CALCULATING = 'calculating',
  CALCULATED = 'calculated',
  REVIEWING = 'reviewing',
  APPROVED = 'approved',
  PROCESSING = 'processing',
  PROCESSED = 'processed',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  PARTIALLY_PROCESSED = 'partially_processed',
  ERROR = 'error',
  REJECTED = 'rejected',
  REVERSED = 'reversed',
}

export enum AnomalyType {
  SALARY_VARIANCE = 'salary_variance',
  HOURS_VARIANCE = 'hours_variance',
  TAX_CALCULATION = 'tax_calculation',
  DEDUCTION_ANOMALY = 'deduction_anomaly',
  DUPLICATE_PAYMENT = 'duplicate_payment',
  MISSING_TIMESHEET = 'missing_timesheet',
  OVERTIME_THRESHOLD = 'overtime_threshold',
  BENEFIT_ELIGIBILITY = 'benefit_eligibility',
  COMPLIANCE_VIOLATION = 'compliance_violation',
  FRAUD_INDICATOR = 'fraud_indicator',
}

export enum AnomallySeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}


