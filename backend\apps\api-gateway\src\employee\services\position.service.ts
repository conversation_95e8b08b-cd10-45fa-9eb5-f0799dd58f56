import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Position } from '@app/database';

@Injectable()
export class PositionService {
  constructor(
    @InjectRepository(Position)
    private readonly positionRepository: Repository<Position>,
  ) {}

  // TODO: Implement position management methods
  async findAll(tenantId: string): Promise<Position[]> {
    return this.positionRepository.find({
      where: { tenantId },
      order: { title: 'ASC' },
    });
  }

  async findById(id: string, tenantId: string): Promise<Position> {
    const position = await this.positionRepository.findOne({
      where: { id, tenantId },
    });

    if (!position) {
      throw new NotFoundException(`Position with ID ${id} not found`);
    }

    return position;
  }
}
