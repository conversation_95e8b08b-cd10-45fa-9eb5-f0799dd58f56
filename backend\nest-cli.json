{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/api-gateway/src", "compilerOptions": {"deleteOutDir": true, "webpack": false, "tsConfigPath": "tsconfig.build.json"}, "monorepo": true, "root": "apps/api-gateway", "projects": {"api-gateway": {"type": "application", "root": "apps/api-gateway", "entryFile": "main", "sourceRoot": "apps/api-gateway/src", "compilerOptions": {"tsConfigPath": "apps/api-gateway/tsconfig.app.json"}}, "api": {"type": "application", "root": "apps/api", "entryFile": "main", "sourceRoot": "apps/api/src", "compilerOptions": {"tsConfigPath": "apps/api/tsconfig.app.json"}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "database": {"type": "library", "root": "libs/database", "entryFile": "index", "sourceRoot": "libs/database/src", "compilerOptions": {"tsConfigPath": "libs/database/tsconfig.lib.json"}}, "security": {"type": "library", "root": "libs/security", "entryFile": "index", "sourceRoot": "libs/security/src", "compilerOptions": {"tsConfigPath": "libs/security/tsconfig.lib.json"}}}}