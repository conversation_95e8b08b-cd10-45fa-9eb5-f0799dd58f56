import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';
import * as crypto from 'crypto';

import { User } from '@app/database/entities/user.entity';
import { MfaBackupCode } from '@app/database/entities/mfa-backup-code.entity';
import { Session } from '@app/database/entities/session.entity';

@Injectable()
export class MfaService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(MfaBackupCode)
    private mfaBackupCodeRepository: Repository<MfaBackupCode>,
    @InjectRepository(Session)
    private sessionRepository: Repository<Session>,
    private configService: ConfigService,
  ) {}

  /**
   * Check if MFA is enabled for user
   */
  async isMfaEnabled(userId: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['mfaEnabled'],
    });

    return user?.mfaEnabled || false;
  }

  /**
   * Generate MFA secret and QR code for setup
   */
  async generateMfaSetup(userId: string): Promise<{
    secret: string;
    qrCode: string;
    backupCodes: string[];
  }> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['email', 'firstName', 'lastName'],
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Generate secret
    const secret = speakeasy.generateSecret({
      name: `${user.firstName} ${user.lastName}`,
      issuer: 'PeopleNest HRMS',
      length: 32,
    });

    // Generate QR code
    const qrCode = await QRCode.toDataURL(secret.otpauth_url);

    // Generate backup codes
    const backupCodes = this.generateBackupCodes();

    // Store secret temporarily (not enabled until verified)
    await this.userRepository.update(userId, {
      mfaSecret: secret.base32,
      mfaEnabled: false, // Will be enabled after verification
    });

    // Store backup codes
    await this.storeBackupCodes(userId, backupCodes);

    return {
      secret: secret.base32,
      qrCode,
      backupCodes,
    };
  }

  /**
   * Verify MFA setup with TOTP code
   */
  async verifyMfaSetup(userId: string, token: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['mfaSecret'],
    });

    if (!user?.mfaSecret) {
      return false;
    }

    const isValid = speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token,
      window: 2, // Allow 2 time steps before/after
    });

    if (isValid) {
      // Enable MFA
      await this.userRepository.update(userId, {
        mfaEnabled: true,
      });
    }

    return isValid;
  }

  /**
   * Verify MFA code (TOTP or backup code)
   */
  async verifyMfaCode(userId: string, code: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['mfaSecret', 'mfaEnabled'],
    });

    if (!user?.mfaEnabled || !user.mfaSecret) {
      return false;
    }

    // Try TOTP first
    const isTotpValid = speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token: code,
      window: 2,
    });

    if (isTotpValid) {
      return true;
    }

    // Try backup code
    const isBackupCodeValid = await this.verifyBackupCode(userId, code);
    
    return isBackupCodeValid;
  }

  /**
   * Disable MFA for user
   */
  async disableMfa(userId: string): Promise<void> {
    await this.userRepository.update(userId, {
      mfaEnabled: false,
      mfaSecret: null,
    });

    // Remove all backup codes
    await this.mfaBackupCodeRepository.delete({ userId });
  }

  /**
   * Generate new backup codes
   */
  async regenerateBackupCodes(userId: string): Promise<string[]> {
    // Remove existing backup codes
    await this.mfaBackupCodeRepository.delete({ userId });

    // Generate new backup codes
    const backupCodes = this.generateBackupCodes();

    // Store new backup codes
    await this.storeBackupCodes(userId, backupCodes);

    return backupCodes;
  }

  /**
   * Check if session has completed MFA verification
   */
  async isSessionMfaVerified(userId: string, sessionId: string): Promise<boolean> {
    const session = await this.sessionRepository.findOne({
      where: { id: sessionId, userId },
      select: ['mfaVerified'],
    });

    return session?.mfaVerified || false;
  }

  /**
   * Mark session as MFA verified
   */
  async markSessionMfaVerified(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      mfaVerified: true,
      mfaVerifiedAt: new Date(),
    });
  }

  /**
   * Get remaining backup codes count
   */
  async getRemainingBackupCodesCount(userId: string): Promise<number> {
    return this.mfaBackupCodeRepository.count({
      where: { userId, isUsed: false },
    });
  }

  /**
   * Verify backup code
   */
  private async verifyBackupCode(userId: string, code: string): Promise<boolean> {
    const hashedCode = this.hashBackupCode(code);
    
    const backupCode = await this.mfaBackupCodeRepository.findOne({
      where: {
        userId,
        code: hashedCode,
        isUsed: false,
      },
    });

    if (!backupCode) {
      return false;
    }

    // Mark backup code as used
    await this.mfaBackupCodeRepository.update(backupCode.id, {
      isUsed: true,
      usedAt: new Date(),
    });

    return true;
  }

  /**
   * Generate backup codes
   */
  private generateBackupCodes(count: number = 10): string[] {
    const codes: string[] = [];
    
    for (let i = 0; i < count; i++) {
      // Generate 8-character alphanumeric code
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
    }

    return codes;
  }

  /**
   * Store backup codes in database
   */
  private async storeBackupCodes(userId: string, codes: string[]): Promise<void> {
    const backupCodes = codes.map(code => ({
      userId,
      code: this.hashBackupCode(code),
      isUsed: false,
    }));

    await this.mfaBackupCodeRepository.save(backupCodes);
  }

  /**
   * Hash backup code for secure storage
   */
  private hashBackupCode(code: string): string {
    return crypto.createHash('sha256').update(code).digest('hex');
  }

  /**
   * Generate TOTP for testing purposes (development only)
   */
  async generateTotpForTesting(userId: string): Promise<string> {
    if (this.configService.get('NODE_ENV') === 'production') {
      throw new Error('TOTP generation for testing is not allowed in production');
    }

    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['mfaSecret'],
    });

    if (!user?.mfaSecret) {
      throw new Error('MFA not set up for user');
    }

    return speakeasy.totp({
      secret: user.mfaSecret,
      encoding: 'base32',
    });
  }
}
