import {
  Entity,
  Column,
  Index,
  ManyToOne,
  Join<PERSON>olumn,
  Unique,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { User } from './user.entity';
import { Permission } from './permission.entity';

export enum PermissionGrantType {
  DIRECT = 'direct',
  ROLE_BASED = 'role_based',
  INHERITED = 'inherited',
  TEMPORARY = 'temporary',
}

@Entity('user_permissions')
@Unique(['userId', 'permissionId', 'tenantId'])
@Index(['userId', 'isActive'])
@Index(['permissionId', 'isActive'])
export class UserPermission extends TenantAwareEntity {
  @Column({
    type: 'uuid',
    comment: 'User ID',
  })
  userId: string;

  @Column({
    type: 'uuid',
    comment: 'Permission ID',
  })
  permissionId: string;

  @Column({
    type: 'enum',
    enum: PermissionGrantType,
    default: PermissionGrantType.DIRECT,
    comment: 'How this permission was granted',
  })
  grantType: PermissionGrantType;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this permission is active',
  })
  @Index()
  isActive: boolean;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When this permission expires',
  })
  expiresAt?: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who granted this permission',
  })
  grantedBy?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When this permission was granted',
  })
  grantedAt?: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who revoked this permission',
  })
  revokedBy?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When this permission was revoked',
  })
  revokedAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Reason for granting this permission',
  })
  grantReason?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Reason for revoking this permission',
  })
  revokeReason?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Conditions or constraints for this permission',
  })
  conditions?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional metadata for this permission grant',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Source system or process that granted this permission',
  })
  source?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Reference ID for external systems',
  })
  externalRef?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this permission can be delegated to others',
  })
  isDelegatable: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this permission was inherited from a role',
  })
  isInherited: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Priority level for conflicting permissions',
  })
  priority: number;

  // Relationships
  @ManyToOne(() => User, user => user.userPermissions, { eager: false })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Permission, permission => permission.userPermissions, { eager: true })
  @JoinColumn({ name: 'permission_id' })
  permission: Permission;

  @ManyToOne(() => User, { eager: false, nullable: true })
  @JoinColumn({ name: 'granted_by' })
  grantor?: User;

  @ManyToOne(() => User, { eager: false, nullable: true })
  @JoinColumn({ name: 'revoked_by' })
  revoker?: User;

  // Virtual properties
  get isExpired(): boolean {
    return this.expiresAt ? this.expiresAt < new Date() : false;
  }

  get isValid(): boolean {
    return this.isActive && !this.isExpired && !this.revokedAt;
  }

  get isRevoked(): boolean {
    return !!this.revokedAt;
  }

  get daysUntilExpiry(): number | null {
    if (!this.expiresAt) return null;
    const diff = this.expiresAt.getTime() - new Date().getTime();
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
  }

  revoke(revokedBy: string, reason?: string): void {
    this.isActive = false;
    this.revokedAt = new Date();
    this.revokedBy = revokedBy;
    this.revokeReason = reason;
  }

  extend(newExpiryDate: Date): void {
    this.expiresAt = newExpiryDate;
  }
}
