import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { employeeService, type Employee, type CreateEmployeeRequest, type EmployeeSearchParams } from './employee-service';

// Mock the API client
const mockApiClient = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn(),
};

vi.mock('./api-client', () => ({
  apiClient: mockApiClient,
}));

describe('EmployeeService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  const mockEmployee: Employee = {
    id: 'emp-123',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    employeeId: 'EMP001',
    hireDate: '2023-01-01',
    status: 'active',
    position: {
      id: 'pos-123',
      title: 'Software Developer',
      department: {
        id: 'dept-123',
        name: 'Engineering',
      },
    },
  };

  const mockEmployeeListResponse = {
    data: [mockEmployee],
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
  };

  describe('getEmployees', () => {
    it('should fetch employees with default parameters', async () => {
      mockApiClient.get.mockResolvedValue({ data: mockEmployeeListResponse });

      const result = await employeeService.getEmployees();

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees', { params: {} });
      expect(result).toEqual(mockEmployeeListResponse);
    });

    it('should fetch employees with search parameters', async () => {
      const searchParams: EmployeeSearchParams = {
        page: 2,
        limit: 20,
        search: 'John',
        department: 'Engineering',
        status: 'active',
        sortBy: 'firstName',
        sortOrder: 'asc',
      };

      mockApiClient.get.mockResolvedValue({ data: mockEmployeeListResponse });

      const result = await employeeService.getEmployees(searchParams);

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees', { params: searchParams });
      expect(result).toEqual(mockEmployeeListResponse);
    });

    it('should handle API errors', async () => {
      const error = new Error('API Error');
      mockApiClient.get.mockRejectedValue(error);

      await expect(employeeService.getEmployees()).rejects.toThrow('API Error');
    });
  });

  describe('getEmployee', () => {
    it('should fetch employee by ID', async () => {
      mockApiClient.get.mockResolvedValue({ data: mockEmployee });

      const result = await employeeService.getEmployee('emp-123');

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees/emp-123');
      expect(result).toEqual(mockEmployee);
    });

    it('should handle non-existent employee', async () => {
      const error = new Error('Employee not found');
      mockApiClient.get.mockRejectedValue(error);

      await expect(employeeService.getEmployee('non-existent')).rejects.toThrow('Employee not found');
    });
  });

  describe('createEmployee', () => {
    const createRequest: CreateEmployeeRequest = {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      positionId: 'pos-123',
      hireDate: '2024-01-01',
      employmentType: 'full_time',
      workLocation: 'Remote',
    };

    it('should create new employee', async () => {
      const newEmployee = { ...mockEmployee, ...createRequest, id: 'emp-456' };
      mockApiClient.post.mockResolvedValue({ data: newEmployee });

      const result = await employeeService.createEmployee(createRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith('/employees', createRequest);
      expect(result).toEqual(newEmployee);
    });

    it('should handle validation errors', async () => {
      const error = new Error('Validation failed');
      mockApiClient.post.mockRejectedValue(error);

      await expect(employeeService.createEmployee(createRequest)).rejects.toThrow('Validation failed');
    });
  });

  describe('updateEmployee', () => {
    const updateRequest = {
      firstName: 'John Updated',
      status: 'inactive' as const,
    };

    it('should update employee', async () => {
      const updatedEmployee = { ...mockEmployee, ...updateRequest };
      mockApiClient.patch.mockResolvedValue({ data: updatedEmployee });

      const result = await employeeService.updateEmployee('emp-123', updateRequest);

      expect(mockApiClient.patch).toHaveBeenCalledWith('/employees/emp-123', updateRequest);
      expect(result).toEqual(updatedEmployee);
    });

    it('should handle update errors', async () => {
      const error = new Error('Update failed');
      mockApiClient.patch.mockRejectedValue(error);

      await expect(employeeService.updateEmployee('emp-123', updateRequest)).rejects.toThrow('Update failed');
    });
  });

  describe('deleteEmployee', () => {
    it('should delete employee', async () => {
      mockApiClient.delete.mockResolvedValue({});

      await employeeService.deleteEmployee('emp-123');

      expect(mockApiClient.delete).toHaveBeenCalledWith('/employees/emp-123');
    });

    it('should handle delete errors', async () => {
      const error = new Error('Delete failed');
      mockApiClient.delete.mockRejectedValue(error);

      await expect(employeeService.deleteEmployee('emp-123')).rejects.toThrow('Delete failed');
    });
  });

  describe('uploadProfilePicture', () => {
    it('should upload profile picture', async () => {
      const file = new File(['image data'], 'profile.jpg', { type: 'image/jpeg' });
      const response = { profilePicture: 'https://example.com/profile.jpg' };
      mockApiClient.post.mockResolvedValue({ data: response });

      const result = await employeeService.uploadProfilePicture('emp-123', file);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/employees/emp-123/profile-picture',
        expect.any(FormData),
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      expect(result).toEqual(response);
    });

    it('should handle upload errors', async () => {
      const file = new File(['image data'], 'profile.jpg', { type: 'image/jpeg' });
      const error = new Error('Upload failed');
      mockApiClient.post.mockRejectedValue(error);

      await expect(employeeService.uploadProfilePicture('emp-123', file)).rejects.toThrow('Upload failed');
    });
  });

  describe('getDirectReports', () => {
    it('should fetch direct reports', async () => {
      const directReports = [mockEmployee];
      mockApiClient.get.mockResolvedValue({ data: directReports });

      const result = await employeeService.getDirectReports('manager-123');

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees/manager-123/direct-reports');
      expect(result).toEqual(directReports);
    });
  });

  describe('getOrganizationalChart', () => {
    it('should fetch organizational chart', async () => {
      const orgChart = {
        employee: mockEmployee,
        manager: { ...mockEmployee, id: 'manager-123' },
        directReports: [{ ...mockEmployee, id: 'report-123' }],
        peers: [{ ...mockEmployee, id: 'peer-123' }],
      };
      mockApiClient.get.mockResolvedValue({ data: orgChart });

      const result = await employeeService.getOrganizationalChart('emp-123');

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees/emp-123/org-chart');
      expect(result).toEqual(orgChart);
    });
  });

  describe('getDepartments', () => {
    it('should fetch all departments from paginated response', async () => {
      const departments = [
        { id: 'dept-123', name: 'Engineering', employeeCount: 10 },
        { id: 'dept-456', name: 'Marketing', employeeCount: 5 },
      ];
      const paginatedResponse = {
        data: departments,
        meta: { page: 1, limit: 10, total: 2, totalPages: 1 }
      };
      mockApiClient.get.mockResolvedValue({ data: paginatedResponse });

      const result = await employeeService.getDepartments();

      expect(mockApiClient.get).toHaveBeenCalledWith('/departments');
      expect(result).toEqual(departments);
    });

    it('should return empty array if no data in response', async () => {
      mockApiClient.get.mockResolvedValue({ data: {} });

      const result = await employeeService.getDepartments();

      expect(result).toEqual([]);
    });
  });

  describe('searchEmployees', () => {
    it('should search employees', async () => {
      const searchResults = [mockEmployee];
      mockApiClient.get.mockResolvedValue({ data: searchResults });

      const result = await employeeService.searchEmployees('John');

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees/search', {
        params: { q: 'John' },
      });
      expect(result).toEqual(searchResults);
    });

    it('should handle empty search results', async () => {
      mockApiClient.get.mockResolvedValue({ data: [] });

      const result = await employeeService.searchEmployees('NonExistent');

      expect(result).toEqual([]);
    });
  });

  describe('getEmployeeStats', () => {
    it('should fetch employee statistics', async () => {
      const stats = {
        total: 100,
        active: 95,
        inactive: 5,
        newHires: 10,
        departures: 3,
        byDepartment: [
          { departmentId: 'dept-123', departmentName: 'Engineering', count: 50 },
          { departmentId: 'dept-456', departmentName: 'Marketing', count: 25 },
        ],
        byPosition: [
          { positionId: 'pos-123', positionTitle: 'Software Developer', count: 30 },
          { positionId: 'pos-456', positionTitle: 'Marketing Manager', count: 10 },
        ],
      };
      mockApiClient.get.mockResolvedValue({ data: stats });

      const result = await employeeService.getEmployeeStats();

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees/stats');
      expect(result).toEqual(stats);
    });
  });

  describe('exportEmployees', () => {
    it('should export employees in default format', async () => {
      const blob = new Blob(['export data'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      mockApiClient.get.mockResolvedValue({ data: blob });

      const result = await employeeService.exportEmployees();

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees/export', {
        params: { format: 'xlsx' },
        responseType: 'blob',
      });
      expect(result).toEqual(blob);
    });

    it('should export employees with custom format and filters', async () => {
      const blob = new Blob(['csv data'], { type: 'text/csv' });
      const filters = { department: 'Engineering', status: 'active' };
      mockApiClient.get.mockResolvedValue({ data: blob });

      const result = await employeeService.exportEmployees('csv', filters);

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees/export', {
        params: { format: 'csv', ...filters },
        responseType: 'blob',
      });
      expect(result).toEqual(blob);
    });
  });

  describe('importEmployees', () => {
    it('should import employees from file', async () => {
      const file = new File(['csv data'], 'employees.csv', { type: 'text/csv' });
      const importResult = {
        success: 10,
        failed: 2,
        errors: [
          { row: 5, error: 'Invalid email format' },
          { row: 8, error: 'Missing required field' },
        ],
      };
      mockApiClient.post.mockResolvedValue({ data: importResult });

      const result = await employeeService.importEmployees(file);

      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/employees/import',
        expect.any(FormData),
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      expect(result).toEqual(importResult);
    });
  });

  describe('onboarding', () => {
    it('should fetch onboarding checklist', async () => {
      const checklist = [
        {
          id: 'item-1',
          title: 'Complete paperwork',
          description: 'Fill out all required forms',
          completed: true,
          category: 'hr' as const,
        },
        {
          id: 'item-2',
          title: 'Setup workstation',
          description: 'Configure laptop and accounts',
          completed: false,
          category: 'it' as const,
        },
      ];
      mockApiClient.get.mockResolvedValue({ data: checklist });

      const result = await employeeService.getOnboardingChecklist('emp-123');

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees/emp-123/onboarding');
      expect(result).toEqual(checklist);
    });

    it('should update onboarding item', async () => {
      mockApiClient.patch.mockResolvedValue({});

      await employeeService.updateOnboardingItem('emp-123', 'item-1', true);

      expect(mockApiClient.patch).toHaveBeenCalledWith('/employees/emp-123/onboarding/item-1', {
        completed: true,
      });
    });
  });

  describe('documents', () => {
    it('should fetch employee documents', async () => {
      const documents = [
        {
          id: 'doc-1',
          name: 'Contract.pdf',
          type: 'application/pdf',
          size: 1024,
          uploadedAt: '2024-01-01T00:00:00Z',
          uploadedBy: 'hr-user',
          category: 'contract' as const,
        },
      ];
      mockApiClient.get.mockResolvedValue({ data: documents });

      const result = await employeeService.getEmployeeDocuments('emp-123');

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees/emp-123/documents');
      expect(result).toEqual(documents);
    });

    it('should upload document', async () => {
      const file = new File(['document data'], 'contract.pdf', { type: 'application/pdf' });
      mockApiClient.post.mockResolvedValue({});

      await employeeService.uploadDocument('emp-123', file, 'contract');

      expect(mockApiClient.post).toHaveBeenCalledWith(
        '/employees/emp-123/documents',
        expect.any(FormData),
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
    });

    it('should delete document', async () => {
      mockApiClient.delete.mockResolvedValue({});

      await employeeService.deleteDocument('emp-123', 'doc-1');

      expect(mockApiClient.delete).toHaveBeenCalledWith('/employees/emp-123/documents/doc-1');
    });
  });

  describe('getPerformanceSummary', () => {
    it('should fetch performance summary', async () => {
      const summary = {
        currentRating: 4.2,
        goalProgress: 85,
        completedReviews: 3,
        pendingReviews: 1,
        recentFeedback: [
          {
            rating: 4,
            comment: 'Great work on the project',
            date: '2024-01-15',
            reviewer: 'Manager Name',
          },
        ],
      };
      mockApiClient.get.mockResolvedValue({ data: summary });

      const result = await employeeService.getPerformanceSummary('emp-123');

      expect(mockApiClient.get).toHaveBeenCalledWith('/employees/emp-123/performance');
      expect(result).toEqual(summary);
    });
  });
});
