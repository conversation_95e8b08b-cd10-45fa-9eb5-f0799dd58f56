import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { UserRole, ROLE_HIERARCHY } from '@app/common/enums/user-role.enum';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { PermissionService } from '../services/permission.service';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private permissionService: PermissionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredRoles) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Check if user has any of the required roles
    const hasRole = await this.hasRequiredRole(user, requiredRoles);
    
    if (!hasRole) {
      throw new ForbiddenException(
        `Access denied. Required roles: ${requiredRoles.join(', ')}`,
      );
    }

    return true;
  }

  private async hasRequiredRole(
    user: any,
    requiredRoles: UserRole[],
  ): Promise<boolean> {
    const userRole = user.role as UserRole;
    
    // Check direct role match
    if (requiredRoles.includes(userRole)) {
      return true;
    }

    // Check role hierarchy (higher roles inherit lower role permissions)
    const userRoleHierarchy = ROLE_HIERARCHY[userRole] || [];
    const hasInheritedRole = requiredRoles.some(role => 
      userRoleHierarchy.includes(role)
    );

    if (hasInheritedRole) {
      return true;
    }

    // Check custom permissions for fine-grained access control
    const hasCustomPermission = await this.permissionService.hasPermission(
      user.id,
      requiredRoles.join(','),
    );

    return hasCustomPermission;
  }
}
