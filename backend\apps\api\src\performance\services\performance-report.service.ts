import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';

import { PerformanceReview, ReviewStatus, OverallRating } from '@app/database/entities/performance-review.entity';
import { Goal, GoalStatus } from '@app/database/entities/goal.entity';
import { Feedback } from '@app/database/entities/feedback.entity';
import { PerformanceMetric } from '@app/database/entities/performance-metric.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { Department } from '@app/database/entities/department.entity';
import { TenantService } from '../../tenant/tenant.service';

export interface PerformanceReportFilters {
  departmentIds?: string[];
  employeeIds?: string[];
  dateFrom?: Date;
  dateTo?: Date;
  reviewTypes?: string[];
  includeGoals?: boolean;
  includeFeedback?: boolean;
  includeMetrics?: boolean;
}

export interface PerformanceReportData {
  summary: {
    totalEmployees: number;
    completedReviews: number;
    averageRating: number;
    goalAchievementRate: number;
    reportPeriod: {
      startDate: Date;
      endDate: Date;
    };
  };
  employeePerformance: Array<{
    employee: {
      id: string;
      name: string;
      department: string;
      position: string;
    };
    review: {
      overallRating: OverallRating | null;
      overallScore: number | null;
      completedDate: Date | null;
      strengths: string[];
      improvementAreas: string[];
    };
    goals: {
      total: number;
      achieved: number;
      inProgress: number;
      achievementRate: number;
    };
    feedback: {
      received: number;
      averageRating: number;
    };
    metrics: Array<{
      metricName: string;
      value: number;
      targetValue: number | null;
      achievement: number | null;
    }>;
  }>;
  departmentAnalysis: Array<{
    department: {
      id: string;
      name: string;
    };
    performance: {
      averageRating: number;
      completedReviews: number;
      totalEmployees: number;
      goalAchievementRate: number;
    };
    topPerformers: Array<{
      employeeId: string;
      employeeName: string;
      rating: number;
    }>;
    improvementAreas: string[];
  }>;
  trends: {
    ratingTrends: Array<{
      period: string;
      averageRating: number;
      completedReviews: number;
    }>;
    goalTrends: Array<{
      period: string;
      achievementRate: number;
      totalGoals: number;
    }>;
  };
}

@Injectable()
export class PerformanceReportService {
  constructor(
    @InjectRepository(PerformanceReview)
    private performanceReviewRepository: Repository<PerformanceReview>,
    @InjectRepository(Goal)
    private goalRepository: Repository<Goal>,
    @InjectRepository(Feedback)
    private feedbackRepository: Repository<Feedback>,
    @InjectRepository(PerformanceMetric)
    private performanceMetricRepository: Repository<PerformanceMetric>,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
    private tenantService: TenantService,
  ) {}

  async generatePerformanceReport(filters: PerformanceReportFilters, tenantId: string): Promise<PerformanceReportData> {
    const startDate = filters.dateFrom || new Date(new Date().getFullYear(), 0, 1);
    const endDate = filters.dateTo || new Date();

    // Get employees based on filters
    const employees = await this.getFilteredEmployees(filters, tenantId);
    const employeeIds = employees.map(emp => emp.id);

    // Generate summary
    const summary = await this.generateSummary(employeeIds, startDate, endDate, tenantId);

    // Generate employee performance data
    const employeePerformance = await this.generateEmployeePerformance(
      employees,
      startDate,
      endDate,
      filters,
      tenantId,
    );

    // Generate department analysis
    const departmentAnalysis = await this.generateDepartmentAnalysis(
      filters.departmentIds,
      startDate,
      endDate,
      tenantId,
    );

    // Generate trends
    const trends = await this.generateTrends(employeeIds, startDate, endDate);

    return {
      summary: {
        ...summary,
        reportPeriod: { startDate, endDate },
      },
      employeePerformance,
      departmentAnalysis,
      trends,
    };
  }

  async exportPerformanceReport(
    filters: PerformanceReportFilters,
    format: 'csv' | 'excel' | 'pdf' = 'excel',
  ): Promise<Buffer> {
    const reportData = await this.generatePerformanceReport(filters);

    switch (format) {
      case 'csv':
        return this.exportToCsv(reportData);
      case 'excel':
        return this.exportToExcel(reportData);
      case 'pdf':
        return this.exportToPdf(reportData);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  private async getFilteredEmployees(filters: PerformanceReportFilters, tenantId: string): Promise<Employee[]> {
    
    const queryBuilder = this.employeeRepository
      .createQueryBuilder('employee')
      .leftJoinAndSelect('employee.department', 'department')
      .leftJoinAndSelect('employee.position', 'position')
      .where('employee.tenantId = :tenantId', { tenantId })
      .andWhere('employee.isActive = :isActive', { isActive: true });

    if (filters.departmentIds && filters.departmentIds.length > 0) {
      queryBuilder.andWhere('employee.departmentId IN (:...departmentIds)', {
        departmentIds: filters.departmentIds,
      });
    }

    if (filters.employeeIds && filters.employeeIds.length > 0) {
      queryBuilder.andWhere('employee.id IN (:...employeeIds)', {
        employeeIds: filters.employeeIds,
      });
    }

    return queryBuilder.getMany();
  }

  private async generateSummary(
    employeeIds: string[],
    startDate: Date,
    endDate: Date,
    tenantId: string,
  ): Promise<{
    totalEmployees: number;
    completedReviews: number;
    averageRating: number;
    goalAchievementRate: number;
  }> {

    const totalEmployees = employeeIds.length;

    const completedReviews = await this.performanceReviewRepository.count({
      where: {
        tenantId,
        employeeId: employeeIds as any,
        status: ReviewStatus.COMPLETED,
        completedDate: Between(startDate, endDate),
      },
    });

    // Calculate average rating
    const reviewsWithScores = await this.performanceReviewRepository.find({
      where: {
        tenantId,
        employeeId: employeeIds as any,
        status: ReviewStatus.COMPLETED,
        completedDate: Between(startDate, endDate),
      },
      select: ['overallScore'],
    });

    const averageRating = reviewsWithScores.length > 0
      ? reviewsWithScores.reduce((sum, review) => sum + (review.overallScore || 0), 0) / reviewsWithScores.length
      : 0;

    // Calculate goal achievement rate
    const totalGoals = await this.goalRepository.count({
      where: {
        tenantId,
        employeeId: employeeIds as any,
        targetDate: Between(startDate, endDate),
      },
    });

    const achievedGoals = await this.goalRepository.count({
      where: {
        tenantId,
        employeeId: employeeIds as any,
        status: GoalStatus.ACHIEVED,
        targetDate: Between(startDate, endDate),
      },
    });

    const goalAchievementRate = totalGoals > 0 ? (achievedGoals / totalGoals) * 100 : 0;

    return {
      totalEmployees,
      completedReviews,
      averageRating,
      goalAchievementRate,
    };
  }

  private async generateEmployeePerformance(
    employees: Employee[],
    startDate: Date,
    endDate: Date,
    filters: PerformanceReportFilters,
    tenantId: string,
  ): Promise<PerformanceReportData['employeePerformance']> {
    const employeePerformance = [];

    for (const employee of employees) {
      // Get latest review
      const review = await this.performanceReviewRepository.findOne({
        where: {
          tenantId,
          employeeId: employee.id,
          status: ReviewStatus.COMPLETED,
          completedDate: Between(startDate, endDate),
        },
        order: { completedDate: 'DESC' },
      });

      // Get goals if requested
      let goals = { total: 0, achieved: 0, inProgress: 0, achievementRate: 0 };
      if (filters.includeGoals) {
        const employeeGoals = await this.goalRepository.find({
          where: {
            tenantId,
            employeeId: employee.id,
            targetDate: Between(startDate, endDate),
          },
        });

        goals = {
          total: employeeGoals.length,
          achieved: employeeGoals.filter(g => g.status === GoalStatus.ACHIEVED).length,
          inProgress: employeeGoals.filter(g => g.status === GoalStatus.IN_PROGRESS).length,
          achievementRate: employeeGoals.length > 0 
            ? (employeeGoals.filter(g => g.status === GoalStatus.ACHIEVED).length / employeeGoals.length) * 100 
            : 0,
        };
      }

      // Get feedback if requested
      let feedback = { received: 0, averageRating: 0 };
      if (filters.includeFeedback) {
        const feedbackCount = await this.feedbackRepository.count({
          where: {
            tenantId,
            recipientId: employee.id,
            submittedDate: Between(startDate, endDate),
          },
        });

        // Calculate average feedback rating (simplified)
        feedback = {
          received: feedbackCount,
          averageRating: 0, // Would need to calculate from competency ratings
        };
      }

      // Get metrics if requested
      let metrics: any[] = [];
      if (filters.includeMetrics) {
        metrics = await this.performanceMetricRepository.find({
          where: {
            tenantId,
            employeeId: employee.id,
            measurementDate: Between(startDate, endDate),
          },
          select: ['metricName', 'value', 'targetValue', 'targetAchievement'],
        });
      }

      employeePerformance.push({
        employee: {
          id: employee.id,
          name: `${employee.firstName} ${employee.lastName}`,
          department: employee.department?.name || 'N/A',
          position: employee.position?.title || 'N/A',
        },
        review: {
          overallRating: review?.overallRating || null,
          overallScore: review?.overallScore || null,
          completedDate: review?.completedDate || null,
          strengths: review?.strengths || [],
          improvementAreas: review?.improvementAreas || [],
        },
        goals,
        feedback,
        metrics: metrics.map(m => ({
          metricName: m.metricName,
          value: m.value,
          targetValue: m.targetValue,
          achievement: m.targetAchievement,
        })),
      });
    }

    return employeePerformance;
  }

  private async generateDepartmentAnalysis(
    departmentIds: string[] | undefined,
    startDate: Date,
    endDate: Date,
    tenantId: string,
  ): Promise<PerformanceReportData['departmentAnalysis']> {
    
    const queryBuilder = this.departmentRepository
      .createQueryBuilder('department')
      .leftJoinAndSelect('department.employees', 'employees')
      .where('department.tenantId = :tenantId', { tenantId });

    if (departmentIds && departmentIds.length > 0) {
      queryBuilder.andWhere('department.id IN (:...departmentIds)', { departmentIds });
    }

    const departments = await queryBuilder.getMany();
    const departmentAnalysis = [];

    for (const department of departments) {
      const employeeIds = department.employees.map(emp => emp.id);
      
      if (employeeIds.length === 0) continue;

      // Calculate department performance metrics
      const departmentReviews = await this.performanceReviewRepository.find({
        where: {
          tenantId,
          employeeId: employeeIds as any,
          status: ReviewStatus.COMPLETED,
          completedDate: Between(startDate, endDate),
        },
        select: ['overallScore', 'employeeId'],
        relations: ['employee'],
      });

      const averageRating = departmentReviews.length > 0
        ? departmentReviews.reduce((sum, review) => sum + (review.overallScore || 0), 0) / departmentReviews.length
        : 0;

      // Get top performers
      const topPerformers = departmentReviews
        .sort((a, b) => (b.overallScore || 0) - (a.overallScore || 0))
        .slice(0, 3)
        .map(review => ({
          employeeId: review.employeeId,
          employeeName: `${review.employee?.firstName} ${review.employee?.lastName}`,
          rating: review.overallScore || 0,
        }));

      // Calculate goal achievement rate
      const departmentGoals = await this.goalRepository.find({
        where: {
          tenantId,
          employeeId: employeeIds as any,
          dueDate: Between(startDate, endDate),
        },
      });

      const achievedGoals = departmentGoals.filter(goal => goal.status === GoalStatus.COMPLETED).length;
      const goalAchievementRate = departmentGoals.length > 0 ? (achievedGoals / departmentGoals.length) * 100 : 0;

      // Get common improvement areas
      const improvementAreas = departmentReviews
        .flatMap(review => review.improvementAreas || [])
        .reduce((acc: Record<string, number>, area: string) => {
          acc[area] = (acc[area] || 0) + 1;
          return acc;
        }, {});

      const topImprovementAreas = Object.entries(improvementAreas)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([area]) => area);

      departmentAnalysis.push({
        department: {
          id: department.id,
          name: department.name,
        },
        performance: {
          averageRating,
          completedReviews: departmentReviews.length,
          totalEmployees: employeeIds.length,
          goalAchievementRate,
        },
        topPerformers,
        improvementAreas: topImprovementAreas,
      });
    }

    return departmentAnalysis;
  }

  private async generateTrends(
    employeeIds: string[],
    startDate: Date,
    endDate: Date,
  ): Promise<PerformanceReportData['trends']> {
    // This would generate monthly trends for the given period
    // Implementation would depend on specific requirements
    return {
      ratingTrends: [],
      goalTrends: [],
    };
  }

  private async exportToCsv(reportData: PerformanceReportData): Promise<Buffer> {
    // Implementation for CSV export
    throw new Error('CSV export not implemented');
  }

  private async exportToExcel(reportData: PerformanceReportData): Promise<Buffer> {
    // Implementation for Excel export using libraries like exceljs
    throw new Error('Excel export not implemented');
  }

  private async exportToPdf(reportData: PerformanceReportData): Promise<Buffer> {
    // Implementation for PDF export using libraries like puppeteer or pdfkit
    throw new Error('PDF export not implemented');
  }
}
