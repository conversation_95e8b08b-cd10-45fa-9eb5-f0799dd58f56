import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Between } from 'typeorm';
import { PayrollPeriod } from '@app/database/entities/payroll-period.entity';
import { CreatePayrollPeriodDto, UpdatePayrollPeriodDto, PayrollPeriodQueryDto } from '../dto/create-payroll-period.dto';
import { PayrollRunStatus } from '@app/common/enums/status.enum';
import { TenantService } from '../../tenant/tenant.service';

@Injectable()
export class PayrollPeriodService {
  private readonly logger = new Logger(PayrollPeriodService.name);

  constructor(
    @InjectRepository(PayrollPeriod)
    private readonly payrollPeriodRepository: Repository<PayrollPeriod>,
    private readonly tenantService: TenantService,
  ) {}

  async create(tenantId: string, createPayrollPeriodDto: CreatePayrollPeriodDto): Promise<PayrollPeriod> {
    this.logger.log(`Creating payroll period for tenant ${tenantId}`);

    // Validate tenant exists
    await this.tenantService.findById(tenantId);

    // Validate dates
    const startDate = new Date(createPayrollPeriodDto.startDate);
    const endDate = new Date(createPayrollPeriodDto.endDate);
    const payDate = new Date(createPayrollPeriodDto.payDate);

    if (startDate >= endDate) {
      throw new BadRequestException('Start date must be before end date');
    }

    if (payDate < endDate) {
      throw new BadRequestException('Pay date must be on or after end date');
    }

    // Check for overlapping periods
    const overlapping = await this.findOverlappingPeriods(tenantId, startDate, endDate);
    if (overlapping.length > 0) {
      throw new BadRequestException('Payroll period overlaps with existing period');
    }

    // Generate unique name if not provided
    let name = createPayrollPeriodDto.name;
    if (!name) {
      name = this.generatePeriodName(startDate, endDate, createPayrollPeriodDto.frequency);
    }

    const payrollPeriod = this.payrollPeriodRepository.create({
      ...createPayrollPeriodDto,
      tenantId,
      name,
      startDate,
      endDate,
      payDate,
      cutoffDate: createPayrollPeriodDto.cutoffDate ? new Date(createPayrollPeriodDto.cutoffDate) : undefined,
      status: PayrollRunStatus.DRAFT,
      employeeCount: 0,
      processedCount: 0,
      failedCount: 0,
      totalGrossPay: 0,
      totalNetPay: 0,
      totalTaxes: 0,
      totalDeductions: 0,
      totalBenefits: 0,
      isLocked: false,
      isCorrectionRun: false,
    });

    const saved = await this.payrollPeriodRepository.save(payrollPeriod);
    this.logger.log(`Created payroll period ${saved.id} for tenant ${tenantId}`);

    return saved;
  }

  async findAll(tenantId: string, query: PayrollPeriodQueryDto): Promise<{ data: PayrollPeriod[]; total: number }> {
    this.logger.log(`Finding payroll periods for tenant ${tenantId}`);

    const where: FindOptionsWhere<PayrollPeriod> = { tenantId };

    // Apply filters
    if (query.frequency) {
      where.frequency = query.frequency;
    }

    if (query.currency) {
      where.currency = query.currency;
    }

    if (query.startDateFrom || query.startDateTo) {
      where.startDate = Between(
        query.startDateFrom ? new Date(query.startDateFrom) : new Date('1900-01-01'),
        query.startDateTo ? new Date(query.startDateTo) : new Date('2100-12-31')
      );
    }

    if (query.payDateFrom || query.payDateTo) {
      where.payDate = Between(
        query.payDateFrom ? new Date(query.payDateFrom) : new Date('1900-01-01'),
        query.payDateTo ? new Date(query.payDateTo) : new Date('2100-12-31')
      );
    }

    if (query.activeOnly) {
      const now = new Date();
      where.startDate = Between(new Date('1900-01-01'), now);
      where.endDate = Between(now, new Date('2100-12-31'));
    }

    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    const [data, total] = await this.payrollPeriodRepository.findAndCount({
      where,
      order: {
        [query.sortBy || 'startDate']: query.sortOrder || 'DESC',
      },
      skip,
      take: limit,
    });

    return { data, total };
  }

  async findOne(tenantId: string, id: string): Promise<PayrollPeriod> {
    this.logger.log(`Finding payroll period ${id} for tenant ${tenantId}`);

    const payrollPeriod = await this.payrollPeriodRepository.findOne({
      where: { id, tenantId },
      relations: ['payslips', 'approver', 'processor'],
    });

    if (!payrollPeriod) {
      throw new NotFoundException(`Payroll period with ID ${id} not found`);
    }

    return payrollPeriod;
  }

  async update(tenantId: string, id: string, updatePayrollPeriodDto: UpdatePayrollPeriodDto): Promise<PayrollPeriod> {
    this.logger.log(`Updating payroll period ${id} for tenant ${tenantId}`);

    const payrollPeriod = await this.findOne(tenantId, id);

    // Check if period can be updated
    if (payrollPeriod.isLocked) {
      throw new BadRequestException('Cannot update locked payroll period');
    }

    if ([PayrollRunStatus.PROCESSING, PayrollRunStatus.PROCESSED, PayrollRunStatus.COMPLETED].includes(payrollPeriod.status)) {
      throw new BadRequestException('Cannot update payroll period in current status');
    }

    // Validate date changes
    if (updatePayrollPeriodDto.startDate || updatePayrollPeriodDto.endDate) {
      const startDate = updatePayrollPeriodDto.startDate ? new Date(updatePayrollPeriodDto.startDate) : payrollPeriod.startDate;
      const endDate = updatePayrollPeriodDto.endDate ? new Date(updatePayrollPeriodDto.endDate) : payrollPeriod.endDate;

      if (startDate >= endDate) {
        throw new BadRequestException('Start date must be before end date');
      }

      // Check for overlapping periods (excluding current period)
      const overlapping = await this.findOverlappingPeriods(tenantId, startDate, endDate, id);
      if (overlapping.length > 0) {
        throw new BadRequestException('Updated dates would overlap with existing period');
      }
    }

    // Validate pay date
    if (updatePayrollPeriodDto.payDate) {
      const payDate = new Date(updatePayrollPeriodDto.payDate);
      const endDate = updatePayrollPeriodDto.endDate ? new Date(updatePayrollPeriodDto.endDate) : payrollPeriod.endDate;

      if (payDate < endDate) {
        throw new BadRequestException('Pay date must be on or after end date');
      }
    }

    Object.assign(payrollPeriod, {
      ...updatePayrollPeriodDto,
      startDate: updatePayrollPeriodDto.startDate ? new Date(updatePayrollPeriodDto.startDate) : payrollPeriod.startDate,
      endDate: updatePayrollPeriodDto.endDate ? new Date(updatePayrollPeriodDto.endDate) : payrollPeriod.endDate,
      payDate: updatePayrollPeriodDto.payDate ? new Date(updatePayrollPeriodDto.payDate) : payrollPeriod.payDate,
      cutoffDate: updatePayrollPeriodDto.cutoffDate ? new Date(updatePayrollPeriodDto.cutoffDate) : payrollPeriod.cutoffDate,
    });

    const updated = await this.payrollPeriodRepository.save(payrollPeriod);
    this.logger.log(`Updated payroll period ${id} for tenant ${tenantId}`);

    return updated;
  }

  async remove(tenantId: string, id: string): Promise<void> {
    this.logger.log(`Removing payroll period ${id} for tenant ${tenantId}`);

    const payrollPeriod = await this.findOne(tenantId, id);

    // Check if period can be deleted
    if (payrollPeriod.isLocked) {
      throw new BadRequestException('Cannot delete locked payroll period');
    }

    if ([PayrollRunStatus.PROCESSING, PayrollRunStatus.PROCESSED, PayrollRunStatus.COMPLETED].includes(payrollPeriod.status)) {
      throw new BadRequestException('Cannot delete payroll period in current status');
    }

    if (payrollPeriod.payslips && payrollPeriod.payslips.length > 0) {
      throw new BadRequestException('Cannot delete payroll period with existing payslips');
    }

    await this.payrollPeriodRepository.remove(payrollPeriod);
    this.logger.log(`Removed payroll period ${id} for tenant ${tenantId}`);
  }

  async lock(tenantId: string, id: string): Promise<PayrollPeriod> {
    this.logger.log(`Locking payroll period ${id} for tenant ${tenantId}`);

    const payrollPeriod = await this.findOne(tenantId, id);

    if (payrollPeriod.isLocked) {
      throw new BadRequestException('Payroll period is already locked');
    }

    if (payrollPeriod.status !== PayrollRunStatus.COMPLETED) {
      throw new BadRequestException('Can only lock completed payroll periods');
    }

    payrollPeriod.isLocked = true;
    const updated = await this.payrollPeriodRepository.save(payrollPeriod);

    this.logger.log(`Locked payroll period ${id} for tenant ${tenantId}`);
    return updated;
  }

  async unlock(tenantId: string, id: string): Promise<PayrollPeriod> {
    this.logger.log(`Unlocking payroll period ${id} for tenant ${tenantId}`);

    const payrollPeriod = await this.findOne(tenantId, id);

    if (!payrollPeriod.isLocked) {
      throw new BadRequestException('Payroll period is not locked');
    }

    payrollPeriod.isLocked = false;
    const updated = await this.payrollPeriodRepository.save(payrollPeriod);

    this.logger.log(`Unlocked payroll period ${id} for tenant ${tenantId}`);
    return updated;
  }

  async getCurrentPeriod(tenantId: string): Promise<PayrollPeriod | null> {
    const now = new Date();
    
    const currentPeriod = await this.payrollPeriodRepository.findOne({
      where: {
        tenantId,
        startDate: Between(new Date('1900-01-01'), now),
        endDate: Between(now, new Date('2100-12-31')),
      },
      order: { startDate: 'DESC' },
    });

    return currentPeriod;
  }

  async getUpcomingPeriods(tenantId: string, limit: number = 5): Promise<PayrollPeriod[]> {
    const now = new Date();
    
    const upcomingPeriods = await this.payrollPeriodRepository.find({
      where: {
        tenantId,
        startDate: Between(now, new Date('2100-12-31')),
      },
      order: { startDate: 'ASC' },
      take: limit,
    });

    return upcomingPeriods;
  }

  private async findOverlappingPeriods(
    tenantId: string,
    startDate: Date,
    endDate: Date,
    excludeId?: string
  ): Promise<PayrollPeriod[]> {
    const query = this.payrollPeriodRepository.createQueryBuilder('period')
      .where('period.tenantId = :tenantId', { tenantId })
      .andWhere('period.startDate < :endDate', { endDate })
      .andWhere('period.endDate > :startDate', { startDate });

    if (excludeId) {
      query.andWhere('period.id != :excludeId', { excludeId });
    }

    return query.getMany();
  }

  private generatePeriodName(startDate: Date, endDate: Date, frequency: string): string {
    const year = startDate.getFullYear();
    const month = startDate.toLocaleString('default', { month: 'long' });
    
    switch (frequency) {
      case 'weekly':
        const weekStart = startDate.toLocaleDateString();
        const weekEnd = endDate.toLocaleDateString();
        return `Week ${weekStart} - ${weekEnd}`;
      case 'bi_weekly':
        return `Bi-weekly ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
      case 'semi_monthly':
        const period = startDate.getDate() <= 15 ? '1st Half' : '2nd Half';
        return `${month} ${year} - ${period}`;
      case 'monthly':
        return `${month} ${year}`;
      case 'quarterly':
        const quarter = Math.ceil((startDate.getMonth() + 1) / 3);
        return `Q${quarter} ${year}`;
      case 'annually':
        return `Annual ${year}`;
      default:
        return `${month} ${year}`;
    }
  }
}
