import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinC<PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { Employee } from './employee.entity';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { PerformanceReview } from './performance-review.entity';

export enum GoalType {
  PERFORMANCE = 'performance',
  DEVELOPMENT = 'development',
  BEHAVIORAL = 'behavioral',
  CAREER = 'career',
  PROJECT = 'project',
  TEAM = 'team',
  COMPANY = 'company',
}

export enum GoalStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  IN_PROGRESS = 'in_progress',
  ON_TRACK = 'on_track',
  AT_RISK = 'at_risk',
  BEHIND = 'behind',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  ACHIEVED = 'achieved',
  CANCELLED = 'cancelled',
  DEFERRED = 'deferred',
}

export enum GoalPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum MeasurementType {
  PERCENTAGE = 'percentage',
  NUMBER = 'number',
  CURRENCY = 'currency',
  BOOLEAN = 'boolean',
  QUALITATIVE = 'qualitative',
}

@Entity('goals')
@Index(['tenantId', 'employeeId'])
@Index(['tenantId', 'status'])
@Index(['tenantId', 'goalType'])
@Index(['tenantId', 'dueDate'])
export class Goal {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'employee_id' })
  employeeId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @Column({ name: 'manager_id', nullable: true })
  managerId: string;

  @ManyToOne(() => Employee, { nullable: true })
  @JoinColumn({ name: 'manager_id' })
  manager: Employee;

  @Column({ name: 'performance_review_id', nullable: true })
  performanceReviewId: string;

  @ManyToOne(() => PerformanceReview, review => review.goals, { nullable: true })
  @JoinColumn({ name: 'performance_review_id' })
  performanceReview: PerformanceReview;

  @Column({ name: 'parent_goal_id', nullable: true })
  parentGoalId: string;

  @ManyToOne(() => Goal, { nullable: true })
  @JoinColumn({ name: 'parent_goal_id' })
  parentGoal: Goal;

  @OneToMany(() => Goal, goal => goal.parentGoal)
  subGoals: Goal[];

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: GoalType,
    name: 'goal_type',
  })
  goalType: GoalType;

  @Column({
    type: 'enum',
    enum: GoalStatus,
    default: GoalStatus.DRAFT,
  })
  status: GoalStatus;

  @Column({
    type: 'enum',
    enum: GoalPriority,
    default: GoalPriority.MEDIUM,
  })
  priority: GoalPriority;

  @Column({ name: 'start_date', type: 'date' })
  startDate: Date;

  @Column({ name: 'due_date', type: 'date' })
  dueDate: Date;

  @Column({ name: 'target_date', type: 'date', nullable: true })
  targetDate?: Date;

  @Column({ name: 'completed_date', type: 'timestamp', nullable: true })
  completedDate: Date;

  // Measurement and Progress
  @Column({
    type: 'enum',
    enum: MeasurementType,
    name: 'measurement_type',
  })
  measurementType: MeasurementType;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true, name: 'target_value' })
  targetValue: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true, name: 'current_value' })
  currentValue: number;

  @Column({ type: 'text', nullable: true, name: 'target_description' })
  targetDescription: string;

  @Column({ type: 'text', nullable: true, name: 'current_description' })
  currentDescription: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0, name: 'progress_percentage' })
  progressPercentage: number;

  // Key Results (OKR style)
  @Column({ type: 'json', nullable: true, name: 'key_results' })
  keyResults: Array<{
    id: string;
    description: string;
    targetValue: number;
    currentValue: number;
    measurementType: MeasurementType;
    weight: number;
    completed: boolean;
  }>;

  // Milestones
  @Column({ type: 'json', nullable: true })
  milestones: Array<{
    id: string;
    title: string;
    description: string;
    dueDate: string;
    completed: boolean;
    completedDate?: string;
    weight: number;
  }>;

  // Success Criteria
  @Column({ type: 'json', nullable: true, name: 'success_criteria' })
  successCriteria: string[];

  // Resources and Support
  @Column({ type: 'json', nullable: true, name: 'required_resources' })
  requiredResources: Array<{
    type: string;
    description: string;
    cost?: number;
    approved: boolean;
  }>;

  @Column({ type: 'json', nullable: true, name: 'support_needed' })
  supportNeeded: Array<{
    type: string;
    description: string;
    fromWhom?: string;
  }>;

  // Alignment
  @Column({ type: 'json', nullable: true, name: 'aligned_objectives' })
  alignedObjectives: Array<{
    level: 'company' | 'department' | 'team';
    objectiveId: string;
    objectiveTitle: string;
  }>;

  // Progress Updates
  @Column({ type: 'json', nullable: true, name: 'progress_updates' })
  progressUpdates: Array<{
    id: string;
    date: string;
    progress: number;
    description: string;
    challenges?: string;
    nextSteps?: string;
    updatedBy: string;
  }>;

  // AI Insights
  @Column({ type: 'json', nullable: true, name: 'ai_insights' })
  aiInsights: {
    achievabilityScore?: number;
    riskFactors?: string[];
    recommendations?: string[];
    similarGoalsPerformance?: {
      averageCompletionRate: number;
      averageTimeToComplete: number;
      successFactors: string[];
    };
  };

  // Metadata
  @Column({ type: 'json', nullable: true })
  metadata: {
    tags?: string[];
    category?: string;
    visibility?: 'private' | 'team' | 'department' | 'company';
    customFields?: Record<string, any>;
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  // Computed properties
  get isOverdue(): boolean {
    return this.dueDate < new Date() && this.status !== GoalStatus.COMPLETED;
  }

  get daysRemaining(): number {
    const today = new Date();
    const timeDiff = this.dueDate.getTime() - today.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  get completionRate(): number {
    if (this.keyResults && this.keyResults.length > 0) {
      const completedResults = this.keyResults.filter(kr => kr.completed).length;
      return (completedResults / this.keyResults.length) * 100;
    }
    return this.progressPercentage;
  }
}
