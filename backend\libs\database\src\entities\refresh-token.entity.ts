import {
  <PERSON>tity,
  Column,
  Index,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BeforeI<PERSON>rt,
  BeforeUpdate,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { User } from './user.entity';

@Entity('refresh_tokens')
@Index(['userId', 'isActive'])
@Index(['tokenHash'], { unique: true })
@Index(['expiresAt'])
export class RefreshToken extends TenantAwareEntity {
  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Hashed refresh token',
  })
  tokenHash: string;

  @Column({
    type: 'uuid',
    comment: 'User ID associated with this token',
  })
  userId: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Token hash alias',
  })
  token?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Session ID',
  })
  sessionId?: string;

  @Column({
    type: 'timestamp with time zone',
    comment: 'Token expiry timestamp',
  })
  @Index()
  expiresAt: Date;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the token is active',
  })
  @Index()
  isActive: boolean;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: 'IP address when token was created',
  })
  ipAddress?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'User agent when token was created',
  })
  userAgent?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Device identifier',
  })
  deviceId?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Last time this token was used',
  })
  lastUsedAt?: Date;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Number of times this token has been used',
  })
  usageCount: number;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Token that replaced this one (for rotation)',
  })
  replacedByToken?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this token has been revoked',
  })
  isRevoked: boolean;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When the token was revoked',
  })
  revokedAt?: Date;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Reason for token revocation',
  })
  revocationReason?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Token family ID for rotation tracking',
  })
  familyId?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional token metadata',
  })
  metadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => User, user => user.refreshTokens, { eager: false })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // Virtual properties
  get isExpired(): boolean {
    return this.expiresAt < new Date();
  }

  get isValid(): boolean {
    return this.isActive && !this.isExpired && !this.isRevoked;
  }

  get ageInDays(): number {
    const age = new Date().getTime() - this.createdAt.getTime();
    return Math.floor(age / (1000 * 60 * 60 * 24));
  }

  @BeforeUpdate()
  updateUsage() {
    if (this.lastUsedAt) {
      this.usageCount += 1;
    }
  }

  @BeforeUpdate()
  checkExpiry() {
    if (this.isExpired && this.isActive) {
      this.isActive = false;
    }
  }

  revoke(reason?: string): void {
    this.isRevoked = true;
    this.isActive = false;
    this.revokedAt = new Date();
    this.revocationReason = reason || 'manual_revocation';
  }

  markAsUsed(): void {
    this.lastUsedAt = new Date();
    this.usageCount += 1;
  }
}
