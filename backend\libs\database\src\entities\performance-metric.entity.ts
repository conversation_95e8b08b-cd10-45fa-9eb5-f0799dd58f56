import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyTo<PERSON>ne,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { Employee } from './employee.entity';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { Department } from './department.entity';

export enum MetricType {
  KPI = 'kpi',
  OKR = 'okr',
  COMPETENCY = 'competency',
  BEHAVIOR = 'behavior',
  SKILL = 'skill',
  PRODUCTIVITY = 'productivity',
  QUALITY = 'quality',
  EFFICIENCY = 'efficiency',
  CUSTOMER_SATISFACTION = 'customer_satisfaction',
  TEAM_COLLABORATION = 'team_collaboration',
}

export enum MetricCategory {
  PERFORMANCE = 'performance',
  DEVELOPMENT = 'development',
  LEADERSHIP = 'leadership',
  TECHNICAL = 'technical',
  SOFT_SKILLS = 'soft_skills',
  BUSINESS_IMPACT = 'business_impact',
  INNOVATION = 'innovation',
  COMPLIANCE = 'compliance',
}

export enum MeasurementUnit {
  PERCENTAGE = 'percentage',
  NUMBER = 'number',
  CURRENCY = 'currency',
  HOURS = 'hours',
  DAYS = 'days',
  RATING = 'rating',
  SCORE = 'score',
  BOOLEAN = 'boolean',
}

export enum AggregationMethod {
  SUM = 'sum',
  AVERAGE = 'average',
  MEDIAN = 'median',
  MIN = 'min',
  MAX = 'max',
  COUNT = 'count',
  LATEST = 'latest',
  WEIGHTED_AVERAGE = 'weighted_average',
}

@Entity('performance_metrics')
@Index(['tenantId', 'employeeId'])
@Index(['tenantId', 'metricType'])
@Index(['tenantId', 'category'])
@Index(['tenantId', 'measurementDate'])
export class PerformanceMetric {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'employee_id' })
  employeeId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @Column({ name: 'department_id', nullable: true })
  departmentId: string;

  @ManyToOne(() => Department, { nullable: true })
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @Column({ length: 255, name: 'metric_name' })
  metricName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: MetricType,
    name: 'metric_type',
  })
  metricType: MetricType;

  @Column({
    type: 'enum',
    enum: MetricCategory,
  })
  category: MetricCategory;

  @Column({
    type: 'enum',
    enum: MeasurementUnit,
    name: 'measurement_unit',
  })
  measurementUnit: MeasurementUnit;

  @Column({
    type: 'enum',
    enum: AggregationMethod,
    name: 'aggregation_method',
  })
  aggregationMethod: AggregationMethod;

  // Current Values
  @Column({ type: 'decimal', precision: 15, scale: 4, name: 'current_value' })
  currentValue: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true, name: 'value' })
  value?: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true, name: 'target_value' })
  targetValue: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true, name: 'baseline_value' })
  baselineValue: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true, name: 'previous_value' })
  previousValue: number;

  // Measurement Period
  @Column({ name: 'measurement_date', type: 'date' })
  measurementDate: Date;

  @Column({ name: 'period_start', type: 'date', nullable: true })
  periodStart: Date;

  @Column({ name: 'period_end', type: 'date', nullable: true })
  periodEnd: Date;

  @Column({ length: 50, nullable: true, name: 'measurement_frequency' })
  measurementFrequency: string; // daily, weekly, monthly, quarterly, annually

  // Performance Analysis
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'performance_score' })
  performanceScore: number; // 0-100 score

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'target_achievement' })
  targetAchievement: number; // Percentage of target achieved

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'trend_percentage' })
  trendPercentage: number; // Change from previous period

  @Column({ length: 50, nullable: true })
  trend: string; // 'improving', 'declining', 'stable'

  // Benchmarking
  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true, name: 'industry_benchmark' })
  industryBenchmark: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true, name: 'company_average' })
  companyAverage: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true, name: 'department_average' })
  departmentAverage: number;

  @Column({ type: 'decimal', precision: 15, scale: 4, nullable: true, name: 'peer_average' })
  peerAverage: number;

  // Weighting and Importance
  @Column({ type: 'decimal', precision: 5, scale: 2, default: 1.0 })
  weight: number;

  @Column({ length: 50, default: 'medium' })
  importance: string; // low, medium, high, critical

  // Data Quality and Validation
  @Column({ type: 'decimal', precision: 5, scale: 2, default: 100, name: 'data_quality_score' })
  dataQualityScore: number;

  @Column({ type: 'boolean', default: true, name: 'is_validated' })
  isValidated: boolean;

  @Column({ name: 'validated_by', nullable: true })
  validatedBy: string;

  @Column({ name: 'validated_at', type: 'timestamp', nullable: true })
  validatedAt: Date;

  // Source and Calculation
  @Column({ length: 255, nullable: true, name: 'data_source' })
  dataSource: string;

  @Column({ type: 'text', nullable: true, name: 'calculation_method' })
  calculationMethod: string;

  @Column({ type: 'json', nullable: true, name: 'source_data' })
  sourceData: Record<string, any>;

  // AI Insights
  @Column({ type: 'json', nullable: true, name: 'ai_insights' })
  aiInsights: {
    anomalyScore?: number;
    isAnomaly?: boolean;
    predictedNextValue?: number;
    confidenceInterval?: {
      lower: number;
      upper: number;
    };
    contributingFactors?: string[];
    recommendations?: string[];
    riskLevel?: 'low' | 'medium' | 'high';
  };

  // Historical Context
  @Column({ type: 'json', nullable: true, name: 'historical_data' })
  historicalData: Array<{
    date: string;
    value: number;
    target?: number;
    note?: string;
  }>;

  // Comments and Notes
  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  comments: Array<{
    id: string;
    date: string;
    author: string;
    comment: string;
  }>;

  // Metadata
  @Column({ type: 'json', nullable: true })
  metadata: {
    tags?: string[];
    customFields?: Record<string, any>;
    reportingLevel?: string;
    visibility?: 'private' | 'team' | 'department' | 'company';
    automatedCollection?: boolean;
    lastSyncDate?: string;
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  // Computed properties
  get isOnTarget(): boolean {
    if (!this.targetValue) return true;
    const tolerance = 0.05; // 5% tolerance
    return Math.abs(this.currentValue - this.targetValue) / this.targetValue <= tolerance;
  }

  get performanceLevel(): string {
    if (!this.targetValue) return 'unknown';
    const achievement = (this.currentValue / this.targetValue) * 100;
    
    if (achievement >= 110) return 'exceeds';
    if (achievement >= 90) return 'meets';
    if (achievement >= 70) return 'partially_meets';
    return 'below';
  }

  get varianceFromTarget(): number {
    if (!this.targetValue) return 0;
    return ((this.currentValue - this.targetValue) / this.targetValue) * 100;
  }
}
