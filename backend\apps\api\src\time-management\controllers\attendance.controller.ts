import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant, UserRole } from '@app/common';
import { AttendanceService } from '../services/attendance.service';

@ApiTags('Attendance')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('attendance')
export class AttendanceController {
  constructor(private readonly attendanceService: AttendanceService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Create attendance record' })
  async create(@Body() createAttendanceDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.attendanceService.create(createAttendanceDto, user.id, tenantId);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get attendance records' })
  async findAll(@Query() query: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.attendanceService.findAll(query, user, tenantId);
  }

  @Post('clock-in')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Clock in' })
  async clockIn(@Body() clockInDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.attendanceService.clockIn(clockInDto.employeeId, tenantId);
  }

  @Post('clock-out')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Clock out' })
  async clockOut(@Body() clockOutDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.attendanceService.clockOut(clockOutDto.employeeId, tenantId);
  }
}
