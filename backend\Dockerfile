# Multi-stage build for PeopleNest Backend
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .

# Generate Prisma client if using Prisma
# RUN npx prisma generate

# Build the application
RUN npm run build

# Production image, copy all the files and run nest
FROM base AS runner
WORKDIR /app
# Install tini for proper signal handling and curl for the healthcheck.
# tini acts as a lightweight init process (PID 1), ensuring that signals
# like SIGTERM from Kubernetes are correctly forwarded to the Node.js process.
RUN apk add --no-cache tini curl
 
# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nestjs

# Copy the built application
COPY --from=builder /app/dist ./dist
# OPTIMIZATION: Copy production dependencies from the 'deps' stage for a smaller image.
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/package*.json ./

# Copy any additional files needed at runtime
COPY --from=builder /app/libs ./libs

# Set ownership to the nestjs user
RUN chown -R nestjs:nodejs /app

# Expose the port the app runs on
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD [ "curl", "-f", "http://localhost:3001/health" ]

# Set environment to production
ENV NODE_ENV=production

# Switch to the non-root user before running the application
USER nestjs

# Use tini as the entrypoint to manage the node process
ENTRYPOINT ["/sbin/tini", "--"]

# Start the application. This is the command that tini will execute and manage.
CMD ["node", "dist/main.js"]
