import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant, UserRole } from '@app/common';
import { WorkScheduleService } from '../services/work-schedule.service';

@ApiTags('Work Schedules')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('work-schedules')
export class WorkScheduleController {
  constructor(private readonly workScheduleService: WorkScheduleService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ summary: 'Create work schedule' })
  async create(@Body() createScheduleDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.workScheduleService.create(createScheduleDto, user.id, tenantId);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get work schedules' })
  async findAll(@Query() query: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.workScheduleService.findAll(query, user, tenantId);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get work schedule by ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.workScheduleService.findOne(id, user, tenantId);
  }

  @Put(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ summary: 'Update work schedule' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateScheduleDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.workScheduleService.update(id, updateScheduleDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ summary: 'Delete work schedule' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.workScheduleService.remove(id, user.id, tenantId);
  }
}
