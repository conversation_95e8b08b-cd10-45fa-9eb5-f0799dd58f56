import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ThrottlerModule } from '@nestjs/throttler';
import { ScheduleModule } from '@nestjs/schedule';

// Core modules
import { DatabaseModule } from '@app/database';
import { SecurityModule } from '@app/security';
import { CommonModule } from '@app/common';

// Feature modules
import { PayrollModule } from './payroll/payroll.module';
import { PerformanceModule } from './performance/performance.module';
import { EmployeeModule } from './employee/employee.module';
import { DepartmentModule } from './department/department.module';
import { PositionModule } from './position/position.module';
import { TimeManagementModule } from './time-management/time-management.module';
import { BenefitsModule } from './benefits/benefits.module';
import { ReportsModule } from './reports/reports.module';
import { NotificationModule } from './notification/notification.module';
import { AuthModule } from './auth/auth.module';

// Controllers and services
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HealthController } from './health/health.controller';
import { HealthService } from './health/health.service';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true,
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('DATABASE_HOST', 'localhost'),
        port: configService.get<number>('DATABASE_PORT', 5433),
        username: configService.get<string>('DATABASE_USERNAME', 'postgres'),
        password: configService.get<string>('DATABASE_PASSWORD', 'postgres123'),
        database: configService.get<string>('DATABASE_NAME', 'peoplenest_dev'),
        ssl: false,
        logging: configService.get<boolean>('DATABASE_LOGGING', false),
        synchronize: false,
        migrationsRun: configService.get<boolean>('DATABASE_MIGRATIONS_RUN', true),
        entities: [__dirname + '/../**/*.entity{.ts,.js}'],
        migrations: [__dirname + '/../migrations/*{.ts,.js}'],
        subscribers: [__dirname + '/../subscribers/*{.ts,.js}'],
        autoLoadEntities: true,
        retryAttempts: 3,
        retryDelay: 3000,
        maxQueryExecutionTime: 10000,
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
        },
      }),
      inject: [ConfigService],
    }),

    // Event system
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        throttlers: [{
          ttl: configService.get<number>('THROTTLE_TTL', 60),
          limit: configService.get<number>('THROTTLE_LIMIT', 100),
        }],
      }),
      inject: [ConfigService],
    }),

    // Task scheduling
    ScheduleModule.forRoot(),

    // Core modules
    DatabaseModule,
    SecurityModule,
    CommonModule,

    // Feature modules
    PayrollModule,
    PerformanceModule,
    EmployeeModule,
    DepartmentModule,
    PositionModule,
    TimeManagementModule,
    BenefitsModule,
    ReportsModule,
    NotificationModule,
    AuthModule,
  ],
  controllers: [
    AppController,
    HealthController,
  ],
  providers: [
    AppService,
    HealthService,
  ],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    const environment = this.configService.get<string>('NODE_ENV', 'development');
    console.log(`🚀 PeopleNest API Module initialized in ${environment} mode`);
  }
}
