import {
  Entity,
  Column,
  Index,
  OneToMany,
  ManyToOne,
  JoinColum<PERSON>,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { User } from './user.entity';
import { Report } from './report.entity';

export enum ReportTemplateType {
  EMPLOYEE = 'employee',
  PAYROLL = 'payroll',
  ATTENDANCE = 'attendance',
  PERFORMANCE = 'performance',
  RECRUITMENT = 'recruitment',
  CUSTOM = 'custom',
}

export enum ReportTemplateFormat {
  PDF = 'pdf',
  EXCEL = 'excel',
  CSV = 'csv',
  JSON = 'json',
}

@Entity('report_templates')
@Index(['name', 'tenantId'])
@Index(['type', 'tenantId'])
export class ReportTemplate extends TenantAwareEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Template name',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Template description',
  })
  description?: string;

  @Column({
    type: 'enum',
    enum: ReportTemplateType,
    comment: 'Report template type',
  })
  type: ReportTemplateType;

  @Column({
    type: 'enum',
    enum: ReportTemplateFormat,
    default: ReportTemplateFormat.PDF,
    comment: 'Default output format',
  })
  format: ReportTemplateFormat;

  @Column({
    type: 'json',
    comment: 'Template configuration',
  })
  config: {
    fields: string[];
    filters?: Record<string, any>;
    sorting?: Record<string, 'ASC' | 'DESC'>;
    grouping?: string[];
    aggregations?: Record<string, string>;
    formatting?: Record<string, any>;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Template layout configuration',
  })
  layout?: {
    header?: string;
    footer?: string;
    logo?: string;
    styles?: Record<string, any>;
  };

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether template is active',
  })
  isActive: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether template is system default',
  })
  isDefault: boolean;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Template metadata',
  })
  metadata?: Record<string, any>;

  // Relationships
  @OneToMany(() => Report, report => report.template)
  reports: Report[];

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  // Virtual properties
  get isSystemTemplate(): boolean {
    return this.isDefault;
  }

  get reportCount(): number {
    return this.reports ? this.reports.length : 0;
  }
}
