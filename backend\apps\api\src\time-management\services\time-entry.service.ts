import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { TimeEntry, Employee } from '@app/database';
import { TimeEntryStatus } from '@app/database/entities/time-entry.entity';

@Injectable()
export class TimeEntryService {
  private readonly logger = new Logger(TimeEntryService.name);

  constructor(
    @InjectRepository(TimeEntry)
    private readonly timeEntryRepository: Repository<TimeEntry>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(createTimeEntryDto: any, createdBy: string, tenantId: string): Promise<any> {
    this.logger.log(`Creating time entry for employee: ${createTimeEntryDto.employeeId}`);

    // Validate employee exists
    const employee = await this.employeeRepository.findOne({
      where: { id: createTimeEntryDto.employeeId, tenantId },
    });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${createTimeEntryDto.employeeId} not found`);
    }

    // Create time entry
    const timeEntry = this.timeEntryRepository.create({
      ...createTimeEntryDto,
      tenantId,
      createdBy,
      updatedBy: createdBy,
      status: 'pending',
    });

    const savedTimeEntry = await this.timeEntryRepository.save(timeEntry);

    // Emit event
    this.eventEmitter.emit('time-entry.created', {
      timeEntryId: savedTimeEntry.id,
      employeeId: createTimeEntryDto.employeeId,
      tenantId,
      createdBy,
    });

    return this.mapToResponseDto(savedTimeEntry);
  }

  async findAll(query: any, user: any, tenantId: string): Promise<any> {
    const { page = 1, limit = 10, employeeId, startDate, endDate } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.timeEntryRepository
      .createQueryBuilder('timeEntry')
      .leftJoinAndSelect('timeEntry.employee', 'employee')
      .where('timeEntry.tenantId = :tenantId', { tenantId });

    // Apply filters
    if (employeeId) {
      queryBuilder.andWhere('timeEntry.employeeId = :employeeId', { employeeId });
    }

    if (startDate) {
      queryBuilder.andWhere('timeEntry.date >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('timeEntry.date <= :endDate', { endDate });
    }

    // Apply role-based filtering
    if (!['admin', 'hr_manager', 'hr_user'].includes(user.role)) {
      // Regular employees can only see their own time entries
      const employee = await this.employeeRepository.findOne({
        where: { userId: user.id, tenantId },
      });

      if (employee) {
        queryBuilder.andWhere('timeEntry.employeeId = :userEmployeeId', {
          userEmployeeId: employee.id,
        });
      }
    }

    // Apply pagination
    queryBuilder.skip(skip).take(limit);
    queryBuilder.orderBy('timeEntry.date', 'DESC');

    const [timeEntries, total] = await queryBuilder.getManyAndCount();

    const data = timeEntries.map(timeEntry => this.mapToResponseDto(timeEntry));

    return {
      data,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(id: string, user: any, tenantId: string): Promise<any> {
    const timeEntry = await this.timeEntryRepository.findOne({
      where: { id, tenantId },
      relations: ['employee'],
    });

    if (!timeEntry) {
      throw new NotFoundException(`Time entry with ID ${id} not found`);
    }

    // Check permissions
    if (!['admin', 'hr_manager', 'hr_user'].includes(user.role)) {
      const employee = await this.employeeRepository.findOne({
        where: { userId: user.id, tenantId },
      });

      if (!employee || timeEntry.employeeId !== employee.id) {
        throw new ForbiddenException('Access denied to this time entry');
      }
    }

    return this.mapToResponseDto(timeEntry);
  }

  async update(id: string, updateTimeEntryDto: any, updatedBy: string, tenantId: string): Promise<any> {
    const timeEntry = await this.timeEntryRepository.findOne({
      where: { id, tenantId },
    });

    if (!timeEntry) {
      throw new NotFoundException(`Time entry with ID ${id} not found`);
    }

    // Update time entry
    Object.assign(timeEntry, updateTimeEntryDto, { updatedBy });
    const savedTimeEntry = await this.timeEntryRepository.save(timeEntry);

    // Emit event
    this.eventEmitter.emit('time-entry.updated', {
      timeEntryId: savedTimeEntry.id,
      tenantId,
      updatedBy,
      changes: updateTimeEntryDto,
    });

    return this.mapToResponseDto(savedTimeEntry);
  }

  async remove(id: string, deletedBy: string, tenantId: string): Promise<void> {
    const timeEntry = await this.timeEntryRepository.findOne({
      where: { id, tenantId },
    });

    if (!timeEntry) {
      throw new NotFoundException(`Time entry with ID ${id} not found`);
    }

    await this.timeEntryRepository.remove(timeEntry);

    // Emit event
    this.eventEmitter.emit('time-entry.deleted', {
      timeEntryId: id,
      tenantId,
      deletedBy,
    });
  }

  async approve(id: string, approvedBy: string, tenantId: string): Promise<any> {
    const timeEntry = await this.timeEntryRepository.findOne({
      where: { id, tenantId },
    });

    if (!timeEntry) {
      throw new NotFoundException(`Time entry with ID ${id} not found`);
    }

    timeEntry.status = TimeEntryStatus.APPROVED;
    timeEntry.approvedBy = approvedBy;
    timeEntry.approvedAt = new Date();
    timeEntry.updatedBy = approvedBy;

    const savedTimeEntry = await this.timeEntryRepository.save(timeEntry);

    // Emit event
    this.eventEmitter.emit('time-entry.approved', {
      timeEntryId: savedTimeEntry.id,
      tenantId,
      approvedBy,
    });

    return this.mapToResponseDto(savedTimeEntry);
  }

  async reject(id: string, reason: string, rejectedBy: string, tenantId: string): Promise<any> {
    const timeEntry = await this.timeEntryRepository.findOne({
      where: { id, tenantId },
    });

    if (!timeEntry) {
      throw new NotFoundException(`Time entry with ID ${id} not found`);
    }

    timeEntry.status = TimeEntryStatus.REJECTED;
    timeEntry.rejectedBy = rejectedBy;
    timeEntry.rejectedAt = new Date();
    timeEntry.rejectionReason = reason;
    timeEntry.updatedBy = rejectedBy;

    const savedTimeEntry = await this.timeEntryRepository.save(timeEntry);

    // Emit event
    this.eventEmitter.emit('time-entry.rejected', {
      timeEntryId: savedTimeEntry.id,
      tenantId,
      rejectedBy,
      reason,
    });

    return this.mapToResponseDto(savedTimeEntry);
  }

  private mapToResponseDto(timeEntry: TimeEntry): any {
    return {
      id: timeEntry.id,
      employeeId: timeEntry.employeeId,
      employee: timeEntry.employee ? {
        id: timeEntry.employee.id,
        fullName: `${timeEntry.employee.firstName} ${timeEntry.employee.lastName}`,
        employeeId: timeEntry.employee.employeeId,
      } : null,
      date: timeEntry.date,
      startTime: timeEntry.startTime,
      endTime: timeEntry.endTime,
      breakDuration: timeEntry.breakDuration,
      totalHours: timeEntry.totalHours,
      description: timeEntry.description,
      status: timeEntry.status,
      approvedBy: timeEntry.approvedBy,
      approvedAt: timeEntry.approvedAt,
      rejectedBy: timeEntry.rejectedBy,
      rejectedAt: timeEntry.rejectedAt,
      rejectionReason: timeEntry.rejectionReason,
      createdAt: timeEntry.createdAt.toISOString(),
      updatedAt: timeEntry.updatedAt.toISOString(),
    };
  }
}
