import {
  Entity,
  Column,
  Index,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Department } from './department.entity';
import { Employee } from './employee.entity';
import { EmploymentType } from '@app/common/enums/status.enum';

export enum PositionLevel {
  ENTRY = 'entry',
  JUNIOR = 'junior',
  MID = 'mid',
  SENIOR = 'senior',
  LEAD = 'lead',
  MANAGER = 'manager',
  DIRECTOR = 'director',
  VP = 'vp',
  C_LEVEL = 'c_level',
}

@Entity('positions')
@Index(['code', 'tenantId'], { unique: true })
@Index(['title', 'departmentId'])
export class Position extends TenantAwareEntity {
  @Column({
    type: 'varchar',
    length: 50,
    comment: 'Position code',
  })
  code: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Position title',
  })
  title: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Position description',
  })
  description?: string;

  @Column({
    type: 'uuid',
    comment: 'Department ID',
  })
  departmentId: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Reports to position ID',
  })
  reportsToId?: string;

  @Column({
    type: 'enum',
    enum: PositionLevel,
    default: PositionLevel.ENTRY,
    comment: 'Position level',
  })
  level: PositionLevel;

  @Column({
    type: 'enum',
    enum: EmploymentType,
    default: EmploymentType.FULL_TIME,
    comment: 'Employment type for this position',
  })
  employmentType: EmploymentType;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Minimum salary range',
  })
  minSalary?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Maximum salary range',
  })
  maxSalary?: number;

  @Column({
    type: 'varchar',
    length: 10,
    default: 'USD',
    comment: 'Salary currency',
  })
  salaryCurrency: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Required skills and qualifications',
  })
  requirements?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Key responsibilities',
  })
  responsibilities?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Required skills as array',
  })
  requiredSkills?: string[];

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Preferred skills as array',
  })
  preferredSkills?: string[];

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Skills required for this position',
  })
  skills?: string[];

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Qualifications required for this position',
  })
  qualifications?: string[];

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Years of experience required',
  })
  experienceYears?: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Minimum years of experience required',
  })
  minExperience?: number;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Required education level',
  })
  educationLevel?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Required certifications',
  })
  requiredCertifications?: string[];

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the position is active',
  })
  @Index()
  isActive: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether remote work is allowed',
  })
  isRemoteAllowed: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether travel is required',
  })
  isTravelRequired: boolean;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Percentage of travel required',
  })
  travelPercentage?: number;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Work location',
  })
  workLocation?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Work schedule requirements',
  })
  workSchedule?: {
    hoursPerWeek?: number;
    isFlexible?: boolean;
    shiftType?: string;
    workDays?: string[];
  };

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display order',
  })
  sortOrder: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Position metadata',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Position creation date',
  })
  positionCreatedDate?: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Position closure date',
  })
  positionClosedDate?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Reason for position closure',
  })
  closureReason?: string;

  // Relationships
  @ManyToOne(() => Department, department => department.positions, { eager: false })
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @ManyToOne(() => Position, { nullable: true })
  @JoinColumn({ name: 'reports_to_id' })
  reportsTo?: Position;

  @OneToMany(() => Employee, employee => employee.position)
  employees: Employee[];

  // Virtual properties
  get employeeCount(): number {
    return this.employees ? this.employees.length : 0;
  }

  get salaryRange(): string {
    if (!this.minSalary || !this.maxSalary) return 'Not specified';
    return `${this.salaryCurrency} ${this.minSalary.toLocaleString()} - ${this.maxSalary.toLocaleString()}`;
  }

  get isOpen(): boolean {
    return this.isActive && !this.positionClosedDate;
  }

  get experienceRange(): string {
    if (!this.minExperience) return 'Not specified';
    return `${this.minExperience}+ years`;
  }
}
