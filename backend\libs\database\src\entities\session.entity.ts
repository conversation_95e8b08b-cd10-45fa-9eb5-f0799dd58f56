import {
  <PERSON><PERSON><PERSON>,
  Column,
  Index,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { User } from './user.entity';

@Entity('sessions')
@Index(['userId', 'isActive'])
@Index(['sessionToken'], { unique: true })
@Index(['expiresAt'])
export class Session extends TenantAwareEntity {
  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Unique session token',
  })
  sessionToken: string;

  @Column({
    type: 'uuid',
    comment: 'User ID associated with this session',
  })
  userId: string;

  @Column({
    type: 'timestamp with time zone',
    comment: 'Session expiry timestamp',
  })
  @Index()
  expiresAt: Date;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the session is active',
  })
  @Index()
  isActive: boolean;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: 'IP address of the session',
  })
  ipAddress?: string;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: 'IP address alias',
  })
  ip?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'User agent string',
  })
  userAgent?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Device type (mobile, desktop, tablet)',
  })
  deviceType?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Operating system',
  })
  operatingSystem?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Browser name and version',
  })
  browser?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Geographic location (city, country)',
  })
  location?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Last activity timestamp',
  })
  lastActivityAt?: Date;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether MFA was verified for this session',
  })
  mfaVerified: boolean;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'MFA verification timestamp',
  })
  mfaVerifiedAt?: Date;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional session metadata',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this session was flagged as suspicious',
  })
  isSuspicious: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a remember me session',
  })
  rememberMe: boolean;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Reason for marking as suspicious',
  })
  suspiciousReason?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When the session was terminated',
  })
  terminatedAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When the session was revoked',
  })
  revokedAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When the session was marked as suspicious',
  })
  suspiciousAt?: Date;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Reason for session termination',
  })
  terminationReason?: string;

  // Relationships
  @ManyToOne(() => User, user => user.sessions, { eager: false })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // Virtual properties
  get isExpired(): boolean {
    return this.expiresAt < new Date();
  }

  get isValid(): boolean {
    return this.isActive && !this.isExpired;
  }

  get durationMinutes(): number {
    if (!this.lastActivityAt) return 0;
    const duration = this.lastActivityAt.getTime() - this.createdAt.getTime();
    return Math.floor(duration / (1000 * 60));
  }

  @BeforeInsert()
  @BeforeUpdate()
  updateActivity() {
    this.lastActivityAt = new Date();
  }

  @BeforeUpdate()
  checkExpiry() {
    if (this.isExpired && this.isActive) {
      this.isActive = false;
      this.terminatedAt = new Date();
      this.terminationReason = 'expired';
    }
  }
}
