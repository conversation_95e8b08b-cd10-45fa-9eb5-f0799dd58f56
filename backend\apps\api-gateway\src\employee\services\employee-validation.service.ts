import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';

import {
  Employee,
  Department,
  Position,
  User,
} from '@app/database';
import { EmployeeStatus } from '@app/common/enums/status.enum';

@Injectable()
export class EmployeeValidationService {
  constructor(
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Position)
    private readonly positionRepository: Repository<Position>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Validate department and position exist and are active
   */
  async validateDepartmentAndPosition(
    departmentId: string,
    positionId: string,
    tenantId: string,
  ): Promise<{ department: Department; position: Position }> {
    // Validate department
    const department = await this.departmentRepository.findOne({
      where: { id: departmentId, tenantId },
    });

    if (!department) {
      throw new NotFoundException('Department not found');
    }

    if (!department.isActive) {
      throw new BadRequestException('Department is not active');
    }

    // Validate position
    const position = await this.positionRepository.findOne({
      where: { id: positionId, tenantId },
    });

    if (!position) {
      throw new NotFoundException('Position not found');
    }

    if (!position.isActive) {
      throw new BadRequestException('Position is not active');
    }

    return { department, position };
  }

  /**
   * Validate manager exists and is active
   */
  async validateManager(
    managerId: string,
    tenantId: string,
  ): Promise<Employee> {
    const manager = await this.employeeRepository.findOne({
      where: { id: managerId, tenantId },
    });

    if (!manager) {
      throw new NotFoundException('Manager not found');
    }

    if (manager.status !== EmployeeStatus.ACTIVE) {
      throw new BadRequestException('Manager is not active');
    }

    return manager;
  }

  /**
   * Validate user account exists and is not already linked to another employee
   */
  async validateUserAccount(
    userId: string,
    tenantId: string,
    excludeEmployeeId?: string,
  ): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: userId, tenantId },
    });

    if (!user) {
      throw new NotFoundException('User account not found');
    }

    // Check if user is already linked to another employee
    const existingEmployee = await this.employeeRepository.findOne({
      where: { userId, tenantId },
    });

    if (existingEmployee && existingEmployee.id !== excludeEmployeeId) {
      throw new ConflictException('User account is already linked to another employee');
    }

    return user;
  }

  /**
   * Validate employee hierarchy (prevent circular references)
   */
  async validateEmployeeHierarchy(
    employeeId: string,
    managerId: string,
    tenantId: string,
  ): Promise<void> {
    if (employeeId === managerId) {
      throw new BadRequestException('Employee cannot be their own manager');
    }

    // Check for circular reference by traversing up the management chain
    let currentManagerId = managerId;
    const visitedManagers = new Set<string>();

    while (currentManagerId) {
      if (visitedManagers.has(currentManagerId)) {
        throw new BadRequestException('Circular management hierarchy detected');
      }

      if (currentManagerId === employeeId) {
        throw new BadRequestException('Cannot create circular management hierarchy');
      }

      visitedManagers.add(currentManagerId);

      const manager = await this.employeeRepository.findOne({
        where: { id: currentManagerId, tenantId },
        select: ['id', 'managerId'],
      });

      if (!manager || !manager.managerId) {
        break;
      }

      currentManagerId = manager.managerId;
    }
  }

  /**
   * Validate employee can be terminated
   */
  async validateEmployeeTermination(
    employeeId: string,
    tenantId: string,
  ): Promise<{ employee: Employee; directReports: Employee[] }> {
    const employee = await this.employeeRepository.findOne({
      where: { id: employeeId, tenantId },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    if (employee.status === EmployeeStatus.TERMINATED) {
      throw new BadRequestException('Employee is already terminated');
    }

    // Check for direct reports
    const directReports = await this.employeeRepository.find({
      where: { managerId: employeeId, tenantId },
    });

    return { employee, directReports };
  }

  /**
   * Validate salary range against position
   */
  async validateSalaryRange(
    positionId: string,
    salary: number,
    tenantId: string,
  ): Promise<void> {
    const position = await this.positionRepository.findOne({
      where: { id: positionId, tenantId },
    });

    if (!position) {
      throw new NotFoundException('Position not found');
    }

    if (position.minSalary && salary < position.minSalary) {
      throw new BadRequestException(
        `Salary ${salary} is below minimum salary ${position.minSalary} for this position`,
      );
    }

    if (position.maxSalary && salary > position.maxSalary) {
      throw new BadRequestException(
        `Salary ${salary} is above maximum salary ${position.maxSalary} for this position`,
      );
    }
  }

  /**
   * Validate employee data consistency
   */
  validateEmployeeData(employeeData: any): void {
    // Validate dates
    if (employeeData.dateOfBirth && employeeData.dateOfJoining) {
      const birthDate = new Date(employeeData.dateOfBirth);
      const joinDate = new Date(employeeData.dateOfJoining);
      const age = joinDate.getFullYear() - birthDate.getFullYear();

      if (age < 16) {
        throw new BadRequestException('Employee must be at least 16 years old at joining date');
      }

      if (age > 100) {
        throw new BadRequestException('Invalid birth date or joining date');
      }
    }

    if (employeeData.dateOfJoining && employeeData.dateOfLeaving) {
      const joinDate = new Date(employeeData.dateOfJoining);
      const leaveDate = new Date(employeeData.dateOfLeaving);

      if (leaveDate <= joinDate) {
        throw new BadRequestException('Leaving date must be after joining date');
      }
    }

    if (employeeData.dateOfJoining && employeeData.probationEndDate) {
      const joinDate = new Date(employeeData.dateOfJoining);
      const probationEndDate = new Date(employeeData.probationEndDate);

      if (probationEndDate <= joinDate) {
        throw new BadRequestException('Probation end date must be after joining date');
      }
    }

    // Validate salary
    if (employeeData.baseSalary && employeeData.baseSalary < 0) {
      throw new BadRequestException('Salary cannot be negative');
    }

    // Validate work schedule
    if (employeeData.workSchedule) {
      const { hoursPerWeek, workDays, startTime, endTime } = employeeData.workSchedule;

      if (hoursPerWeek && (hoursPerWeek < 0 || hoursPerWeek > 168)) {
        throw new BadRequestException('Hours per week must be between 0 and 168');
      }

      if (workDays && workDays.length > 7) {
        throw new BadRequestException('Cannot have more than 7 work days');
      }

      if (startTime && endTime) {
        const start = new Date(`1970-01-01T${startTime}:00`);
        const end = new Date(`1970-01-01T${endTime}:00`);

        if (end <= start) {
          throw new BadRequestException('End time must be after start time');
        }
      }
    }
  }

  /**
   * Validate bulk operation
   */
  async validateBulkOperation(
    employeeIds: string[],
    tenantId: string,
    operation: string,
  ): Promise<Employee[]> {
    if (!employeeIds || employeeIds.length === 0) {
      throw new BadRequestException('No employee IDs provided');
    }

    if (employeeIds.length > 100) {
      throw new BadRequestException('Cannot perform bulk operation on more than 100 employees');
    }

    const employees = await this.employeeRepository.find({
      where: { id: In(employeeIds), tenantId },
    });

    if (employees.length !== employeeIds.length) {
      const foundIds = employees.map(e => e.id);
      const missingIds = employeeIds.filter(id => !foundIds.includes(id));
      throw new NotFoundException(`Employees not found: ${missingIds.join(', ')}`);
    }

    // Validate operation-specific constraints
    switch (operation) {
      case 'terminate':
        const activeEmployees = employees.filter(e => e.status === EmployeeStatus.ACTIVE);
        if (activeEmployees.length === 0) {
          throw new BadRequestException('No active employees to terminate');
        }
        break;

      case 'activate':
        const inactiveEmployees = employees.filter(e => e.status !== EmployeeStatus.ACTIVE);
        if (inactiveEmployees.length === 0) {
          throw new BadRequestException('No inactive employees to activate');
        }
        break;
    }

    return employees;
  }
}
