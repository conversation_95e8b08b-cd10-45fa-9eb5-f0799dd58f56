# Multi-stage build for PeopleNest AI Service

# ---- Base Stage ----
# Use a slim Python image as the base for all stages
FROM python:3.11-slim AS base
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# ---- Build-Base Stage ----
# This stage includes build-time system dependencies needed for installing Python packages.
# These will not be included in the final image, significantly reducing its size.
FROM base AS build-base
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# ---- Dependencies Stage ----
# Install only production Python dependencies.
FROM build-base AS deps
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

# ---- Builder Stage ----
# This stage builds the final application artifact, including downloading ML models.
FROM build-base AS builder
WORKDIR /app

# Copy installed dependencies from the previous stage to leverage caching
COPY --from=deps /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# Copy application source code
COPY . .

# Set cache directories for models inside the workdir for easy and correct copying later
ENV NLTK_DATA=/app/nltk_data
ENV TRANSFORMERS_CACHE=/app/model_cache/transformers
ENV SENTENCE_TRANSFORMERS_HOME=/app/model_cache/sentence_transformers

# Download and prepare all ML models. This layer is large but will be cached effectively.
# This fixes a bug where transformer models were not copied to the final stage.
RUN python -m spacy download en_core_web_sm && \
    python -c "
import nltk
from transformers import AutoTokenizer, AutoModel
from sentence_transformers import SentenceTransformer

# List of NLTK packages to download
nltk_packages = ['punkt', 'stopwords', 'wordnet', 'averaged_perceptron_tagger']
for package in nltk_packages:
    nltk.download(package, download_dir='/app/nltk_data')

# Pre-download and cache transformer models
AutoTokenizer.from_pretrained('bert-base-uncased')
AutoModel.from_pretrained('bert-base-uncased')
SentenceTransformer('all-MiniLM-L6-v2')
"

# ---- Runner Stage ----
# This is the final, lean, and secure production image.
FROM base AS runner
WORKDIR /app

# Install only essential runtime system dependencies and create the non-root user.
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && groupadd --gid 1001 python \
    && useradd --uid 1001 --gid python --shell /bin/bash --create-home python

# Copy installed python packages from the 'deps' stage
COPY --from=deps /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# Copy the entire application artifact (code + all models) from the 'builder' stage
COPY --from=builder /app /app

# Set ownership
RUN chown -R python:python /app /home/<USER>

# Switch to non-root user
USER python

# Set environment variables for the running application so it finds the cached models
ENV NLTK_DATA=/app/nltk_data
ENV TRANSFORMERS_CACHE=/app/model_cache/transformers
ENV SENTENCE_TRANSFORMERS_HOME=/app/model_cache/sentence_transformers
ENV ENVIRONMENT=production

EXPOSE 8003

HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8003/health || exit 1

CMD ["python", "main.py"]
