import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Tenant } from '@app/database/entities/tenant.entity';

export interface EncryptionResult {
  encryptedData: string;
  keyId: string;
  algorithm: string;
  iv: string;
  authTag: string;
}

export interface DecryptionOptions {
  keyId: string;
  algorithm: string;
  iv: string;
  authTag: string;
}

@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly algorithm: string;
  private readonly keyLength: number;
  private readonly ivLength: number;
  private readonly tagLength: number;
  private readonly masterKey: Buffer;
  private readonly keyCache = new Map<string, Buffer>();

  constructor(
    private readonly configService: ConfigService,
    @InjectRepository(Tenant)
    private readonly tenantRepository: Repository<Tenant>,
  ) {
    this.algorithm = this.configService.get<string>('ENCRYPTION_ALGORITHM', 'aes-256-gcm');
    this.keyLength = 32; // 256 bits
    this.ivLength = 16; // 128 bits
    this.tagLength = 16; // 128 bits
    
    const masterKeyHex = this.configService.get<string>('ENCRYPTION_MASTER_KEY');
    if (!masterKeyHex || masterKeyHex.length !== 64) {
      throw new Error('ENCRYPTION_MASTER_KEY must be a 64-character hex string (256 bits)');
    }
    
    this.masterKey = Buffer.from(masterKeyHex, 'hex');
  }

  /**
   * Encrypt sensitive data with tenant-specific key
   */
  async encryptData(data: string, tenantId: string): Promise<EncryptionResult> {
    try {
      if (!data || data.trim() === '') {
        throw new Error('Data to encrypt cannot be empty');
      }

      const tenantKey = await this.getTenantKey(tenantId);
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipher(this.algorithm, tenantKey);
      // Note: For GCM mode, we would use createCipherGCM but need proper algorithm
      // cipher.setAAD(Buffer.from(tenantId)); // Additional authenticated data

      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // const authTag = cipher.getAuthTag(); // Only for GCM mode
      const authTag = Buffer.alloc(16); // Placeholder for non-GCM mode

      const result: EncryptionResult = {
        encryptedData: encrypted,
        keyId: tenantId,
        algorithm: this.algorithm,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
      };

      this.logger.debug(`Data encrypted for tenant ${tenantId}`);
      return result;
    } catch (error) {
      this.logger.error(`Encryption failed for tenant ${tenantId}:`, error);
      throw new Error('Data encryption failed');
    }
  }

  /**
   * Decrypt sensitive data using tenant-specific key
   */
  async decryptData(encryptedData: string, options: DecryptionOptions): Promise<string> {
    try {
      if (!encryptedData || !options.keyId) {
        throw new Error('Invalid encryption data or options');
      }

      const tenantKey = await this.getTenantKey(options.keyId);
      const decipher = crypto.createDecipher(options.algorithm, tenantKey);
      // Note: For GCM mode, we would use createDecipherGCM but need proper algorithm
      // decipher.setAAD(Buffer.from(options.keyId));
      // decipher.setAuthTag(Buffer.from(options.authTag, 'hex'));

      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      this.logger.debug(`Data decrypted for tenant ${options.keyId}`);
      return decrypted;
    } catch (error) {
      this.logger.error(`Decryption failed for tenant ${options.keyId}:`, error);
      throw new Error('Data decryption failed');
    }
  }

  /**
   * Encrypt PII data with additional metadata
   */
  async encryptPII(data: Record<string, any>, tenantId: string): Promise<{
    encryptedData: string;
    metadata: EncryptionResult;
    fieldMap: Record<string, boolean>;
  }> {
    const sensitiveFields = this.identifySensitiveFields(data);
    const processedData = { ...data };
    
    // Encrypt sensitive fields
    for (const [field, value] of Object.entries(data)) {
      if (sensitiveFields[field] && value) {
        processedData[field] = await this.encryptData(String(value), tenantId);
      }
    }

    const serializedData = JSON.stringify(processedData);
    const encryptionResult = await this.encryptData(serializedData, tenantId);

    return {
      encryptedData: encryptionResult.encryptedData,
      metadata: encryptionResult,
      fieldMap: sensitiveFields,
    };
  }

  /**
   * Decrypt PII data with field mapping
   */
  async decryptPII(
    encryptedData: string,
    metadata: DecryptionOptions,
    fieldMap: Record<string, boolean>,
  ): Promise<Record<string, any>> {
    const decryptedJson = await this.decryptData(encryptedData, metadata);
    const data = JSON.parse(decryptedJson);

    // Decrypt individual sensitive fields
    for (const [field, isEncrypted] of Object.entries(fieldMap)) {
      if (isEncrypted && data[field]) {
        try {
          data[field] = await this.decryptData(data[field].encryptedData, data[field]);
        } catch (error) {
          this.logger.warn(`Failed to decrypt field ${field}:`, error);
          data[field] = '[ENCRYPTED]';
        }
      }
    }

    return data;
  }

  /**
   * Generate tenant-specific encryption key
   */
  async generateTenantKey(tenantId: string): Promise<string> {
    const keyDerivation = crypto.createHmac('sha256', this.masterKey);
    keyDerivation.update(tenantId);
    keyDerivation.update('tenant-encryption-key');
    
    const derivedKey = keyDerivation.digest();
    const keyHex = derivedKey.toString('hex');

    // Store the key in tenant record
    await this.tenantRepository.update(
      { id: tenantId },
      { encryptionKey: keyHex }
    );

    // Cache the key
    this.keyCache.set(tenantId, derivedKey);

    this.logger.log(`Generated encryption key for tenant ${tenantId}`);
    return keyHex;
  }

  /**
   * Get tenant encryption key (from cache or database)
   */
  private async getTenantKey(tenantId: string): Promise<Buffer> {
    // Check cache first
    if (this.keyCache.has(tenantId)) {
      return this.keyCache.get(tenantId)!;
    }

    // Get from database
    const tenant = await this.tenantRepository.findOne({
      where: { id: tenantId },
      select: ['encryptionKey'],
    });

    if (!tenant?.encryptionKey) {
      // Generate new key if not exists
      await this.generateTenantKey(tenantId);
      return this.getTenantKey(tenantId);
    }

    const key = Buffer.from(tenant.encryptionKey, 'hex');
    this.keyCache.set(tenantId, key);
    
    return key;
  }

  /**
   * Identify sensitive fields that need encryption
   */
  private identifySensitiveFields(data: Record<string, any>): Record<string, boolean> {
    const sensitivePatterns = [
      /email/i,
      /phone/i,
      /ssn|social.*security/i,
      /tax.*id/i,
      /passport/i,
      /address/i,
      /birth.*date|dob/i,
      /salary|wage|compensation/i,
      /bank.*account|routing/i,
      /credit.*card|payment/i,
      /medical|health/i,
      /emergency.*contact/i,
    ];

    const fieldMap: Record<string, boolean> = {};

    for (const field of Object.keys(data)) {
      fieldMap[field] = sensitivePatterns.some(pattern => pattern.test(field));
    }

    return fieldMap;
  }

  /**
   * Hash data for indexing (one-way)
   */
  hashForIndex(data: string, salt?: string): string {
    const hashSalt = salt || this.configService.get<string>('INDEX_HASH_SALT', 'default-salt');
    const hash = crypto.createHmac('sha256', hashSalt);
    hash.update(data);
    return hash.digest('hex');
  }

  /**
   * Generate secure random token
   */
  generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Encrypt file data
   */
  async encryptFile(fileBuffer: Buffer, tenantId: string): Promise<{
    encryptedBuffer: Buffer;
    metadata: EncryptionResult;
  }> {
    const base64Data = fileBuffer.toString('base64');
    const encryptionResult = await this.encryptData(base64Data, tenantId);
    
    return {
      encryptedBuffer: Buffer.from(encryptionResult.encryptedData, 'hex'),
      metadata: encryptionResult,
    };
  }

  /**
   * Decrypt file data
   */
  async decryptFile(encryptedBuffer: Buffer, metadata: DecryptionOptions): Promise<Buffer> {
    const encryptedData = encryptedBuffer.toString('hex');
    const decryptedBase64 = await this.decryptData(encryptedData, metadata);
    return Buffer.from(decryptedBase64, 'base64');
  }

  /**
   * Key rotation for tenant
   */
  async rotateTenantKey(tenantId: string): Promise<void> {
    this.logger.log(`Starting key rotation for tenant ${tenantId}`);
    
    // Generate new key
    await this.generateTenantKey(tenantId);
    
    // TODO: Implement data re-encryption with new key
    // This would involve:
    // 1. Fetching all encrypted data for the tenant
    // 2. Decrypting with old key
    // 3. Re-encrypting with new key
    // 4. Updating database records
    
    this.logger.log(`Key rotation completed for tenant ${tenantId}`);
  }

  /**
   * Clear key cache (for security)
   */
  clearKeyCache(): void {
    this.keyCache.clear();
    this.logger.log('Encryption key cache cleared');
  }

  /**
   * Validate encryption configuration
   */
  validateConfiguration(): boolean {
    try {
      // Test encryption/decryption
      const testData = 'test-data-for-validation';
      const testTenantId = 'test-tenant';
      
      // This would fail if configuration is invalid
      crypto.createCipher(this.algorithm, this.masterKey);
      
      return true;
    } catch (error) {
      this.logger.error('Encryption configuration validation failed:', error);
      return false;
    }
  }
}
