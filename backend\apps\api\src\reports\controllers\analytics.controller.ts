import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentTenant, UserRole } from '@app/common';
import { AnalyticsService } from '../services/analytics.service';

@ApiTags('Analytics')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('employees')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get employee analytics' })
  async getEmployeeAnalytics(@CurrentTenant() tenantId: string) {
    return this.analyticsService.getEmployeeAnalytics(tenantId);
  }

  @Get('departments')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get department analytics' })
  async getDepartmentAnalytics(@CurrentTenant() tenantId: string) {
    return this.analyticsService.getDepartmentAnalytics(tenantId);
  }

  @Get('payroll')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.FINANCE)
  @ApiOperation({ summary: 'Get payroll analytics' })
  async getPayrollAnalytics(@CurrentTenant() tenantId: string) {
    return this.analyticsService.getPayrollAnalytics(tenantId);
  }

  @Get('time')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get time analytics' })
  async getTimeAnalytics(@CurrentTenant() tenantId: string) {
    return this.analyticsService.getTimeAnalytics(tenantId);
  }

  @Get('performance')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get performance analytics' })
  async getPerformanceAnalytics(@CurrentTenant() tenantId: string) {
    return this.analyticsService.getPerformanceAnalytics(tenantId);
  }
}
