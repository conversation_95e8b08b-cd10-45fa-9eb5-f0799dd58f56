import { Injectable, Logger } from '@nestjs/common';
import { ServiceDiscoveryService, ServiceInstance } from './service-discovery.service';

export type LoadBalancingStrategy = 'round-robin' | 'least-connections' | 'random' | 'weighted';

interface ServiceStats {
  activeConnections: number;
  totalRequests: number;
  lastUsed: Date;
  weight: number;
}

@Injectable()
export class LoadBalancerService {
  private readonly logger = new Logger(LoadBalancerService.name);
  private readonly serviceStats = new Map<string, ServiceStats>();
  private readonly roundRobinCounters = new Map<string, number>();

  constructor(private readonly serviceDiscovery: ServiceDiscoveryService) {}

  async selectInstance(
    serviceName: string,
    strategy: LoadBalancingStrategy = 'round-robin'
  ): Promise<ServiceInstance | null> {
    const instances = await this.serviceDiscovery.getService(serviceName);
    const healthyInstances = instances.filter((instance: any) => instance.health === 'healthy');

    if (healthyInstances.length === 0) {
      this.logger.warn(`No healthy instances available for service: ${serviceName}`);
      return null;
    }

    if (healthyInstances.length === 1) {
      return healthyInstances[0];
    }

    switch (strategy) {
      case 'round-robin':
        return this.roundRobinSelection(serviceName, healthyInstances);
      case 'least-connections':
        return this.leastConnectionsSelection(healthyInstances);
      case 'random':
        return this.randomSelection(healthyInstances);
      case 'weighted':
        return this.weightedSelection(healthyInstances);
      default:
        return this.roundRobinSelection(serviceName, healthyInstances);
    }
  }

  recordRequest(instanceId: string): void {
    const stats = this.getOrCreateStats(instanceId);
    stats.activeConnections++;
    stats.totalRequests++;
    stats.lastUsed = new Date();
  }

  recordResponse(instanceId: string): void {
    const stats = this.getOrCreateStats(instanceId);
    stats.activeConnections = Math.max(0, stats.activeConnections - 1);
  }

  getServiceStats(instanceId: string): ServiceStats {
    return this.getOrCreateStats(instanceId);
  }

  getAllStats(): Map<string, ServiceStats> {
    return new Map(this.serviceStats);
  }

  private roundRobinSelection(serviceName: string, instances: ServiceInstance[]): ServiceInstance {
    const counter = this.roundRobinCounters.get(serviceName) || 0;
    const selectedIndex = counter % instances.length;
    this.roundRobinCounters.set(serviceName, counter + 1);
    
    return instances[selectedIndex];
  }

  private leastConnectionsSelection(instances: ServiceInstance[]): ServiceInstance {
    let selectedInstance = instances[0];
    let minConnections = this.getOrCreateStats(selectedInstance.id).activeConnections;

    for (const instance of instances) {
      const connections = this.getOrCreateStats(instance.id).activeConnections;
      if (connections < minConnections) {
        minConnections = connections;
        selectedInstance = instance;
      }
    }

    return selectedInstance;
  }

  private randomSelection(instances: ServiceInstance[]): ServiceInstance {
    const randomIndex = Math.floor(Math.random() * instances.length);
    return instances[randomIndex];
  }

  private weightedSelection(instances: ServiceInstance[]): ServiceInstance {
    const totalWeight = instances.reduce((sum, instance) => {
      return sum + this.getOrCreateStats(instance.id).weight;
    }, 0);

    if (totalWeight === 0) {
      return this.randomSelection(instances);
    }

    let random = Math.random() * totalWeight;
    
    for (const instance of instances) {
      const weight = this.getOrCreateStats(instance.id).weight;
      random -= weight;
      if (random <= 0) {
        return instance;
      }
    }

    return instances[instances.length - 1];
  }

  private getOrCreateStats(instanceId: string): ServiceStats {
    if (!this.serviceStats.has(instanceId)) {
      this.serviceStats.set(instanceId, {
        activeConnections: 0,
        totalRequests: 0,
        lastUsed: new Date(),
        weight: 1,
      });
    }
    return this.serviceStats.get(instanceId)!;
  }

  setInstanceWeight(instanceId: string, weight: number): void {
    const stats = this.getOrCreateStats(instanceId);
    stats.weight = Math.max(0, weight);
  }

  resetStats(instanceId?: string): void {
    if (instanceId) {
      this.serviceStats.delete(instanceId);
    } else {
      this.serviceStats.clear();
      this.roundRobinCounters.clear();
    }
  }
}
