import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { PayrollPeriod } from '@app/database/entities/payroll-period.entity';
import { Payslip } from '@app/database/entities/payslip.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { ProcessPayrollDto, PayrollProcessingResultDto } from '../dto/payroll-calculation.dto';
import { PayrollRunStatus, PayrollStatus, PaymentMethod } from '@app/common/enums/status.enum';
import { PayrollPeriodService } from './payroll-period.service';
import { PayrollCalculationService } from './payroll-calculation.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

export interface PaymentInstruction {
  employeeId: string;
  employeeName: string;
  amount: number;
  currency: string;
  paymentMethod: PaymentMethod;
  bankAccount?: {
    routingNumber: string;
    accountNumber: string;
    accountType: string;
  };
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  payslipId: string;
  payrollPeriodId: string;
}

export interface PaymentResult {
  employeeId: string;
  payslipId: string;
  success: boolean;
  transactionId?: string;
  errorMessage?: string;
  processedAt: Date;
}

@Injectable()
export class PayrollProcessingService {
  private readonly logger = new Logger(PayrollProcessingService.name);

  constructor(
    @InjectRepository(PayrollPeriod)
    private readonly payrollPeriodRepository: Repository<PayrollPeriod>,
    @InjectRepository(Payslip)
    private readonly payslipRepository: Repository<Payslip>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    private readonly payrollPeriodService: PayrollPeriodService,
    private readonly payrollCalculationService: PayrollCalculationService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async processPayroll(tenantId: string, processPayrollDto: ProcessPayrollDto): Promise<PayrollProcessingResultDto> {
    this.logger.log(`Starting payroll processing for tenant ${tenantId}, period ${processPayrollDto.payrollPeriodId}`);

    // Get payroll period
    const payrollPeriod = await this.payrollPeriodService.findOne(tenantId, processPayrollDto.payrollPeriodId);

    // Validate payroll period status
    if (![PayrollRunStatus.CALCULATED, PayrollRunStatus.APPROVED].includes(payrollPeriod.status)) {
      throw new BadRequestException('Payroll period must be calculated and approved before processing');
    }

    // Update payroll period status
    payrollPeriod.status = PayrollRunStatus.PROCESSING;
    payrollPeriod.processingStartedAt = new Date();
    await this.payrollPeriodRepository.save(payrollPeriod);

    const result: PayrollProcessingResultDto = {
      payrollPeriodId: payrollPeriod.id,
      totalProcessed: 0,
      successfulPayments: 0,
      failedPayments: 0,
      totalAmount: 0,
      processingStartedAt: payrollPeriod.processingStartedAt,
      processingCompletedAt: new Date(),
      errors: [],
    };

    try {
      // Get payslips to process
      const payslips = await this.getPayslipsToProcess(tenantId, processPayrollDto);

      // Validate payslips before processing
      const validationResult = await this.validatePayslipsForProcessing(payslips);
      if (!validationResult.isValid) {
        throw new BadRequestException(`Payroll validation failed: ${validationResult.errors.join(', ')}`);
      }

      // Generate payment instructions
      const paymentInstructions = await this.generatePaymentInstructions(payslips);

      // Process payments
      const paymentResults = await this.processPayments(paymentInstructions, processPayrollDto);

      // Update payslips with payment results
      await this.updatePayslipsWithResults(payslips, paymentResults);

      // Calculate results
      result.totalProcessed = payslips.length;
      result.successfulPayments = paymentResults.filter(r => r.success).length;
      result.failedPayments = paymentResults.filter(r => !r.success).length;
      result.totalAmount = payslips.reduce((sum, p) => sum + p.netPay, 0);
      result.errors = paymentResults
        .filter(r => !r.success)
        .map(r => ({
          employeeId: r.employeeId,
          employeeName: payslips.find(p => p.employeeId === r.employeeId)?.employee?.firstName + ' ' + 
                       payslips.find(p => p.employeeId === r.employeeId)?.employee?.lastName || 'Unknown',
          error: r.errorMessage || 'Payment processing failed',
          severity: 'error' as const,
        }));

      // Update payroll period with final status
      payrollPeriod.status = result.failedPayments > 0 ? PayrollRunStatus.PARTIALLY_PROCESSED : PayrollRunStatus.PROCESSED;
      payrollPeriod.processingCompletedAt = new Date();
      payrollPeriod.processedCount = result.successfulPayments;
      payrollPeriod.failedCount = result.failedPayments;
      await this.payrollPeriodRepository.save(payrollPeriod);

      // Send notifications if requested
      if (processPayrollDto.sendNotifications) {
        await this.sendPayrollNotifications(payslips, paymentResults);
      }

      // Emit payroll processed event
      this.eventEmitter.emit('payroll.processed', {
        tenantId,
        payrollPeriodId: payrollPeriod.id,
        result,
      });

      this.logger.log(`Completed payroll processing for tenant ${tenantId}. Success: ${result.successfulPayments}, Failed: ${result.failedPayments}`);

      return result;

    } catch (error) {
      // Update payroll period status on error
      payrollPeriod.status = PayrollRunStatus.ERROR;
      payrollPeriod.processingCompletedAt = new Date();
      await this.payrollPeriodRepository.save(payrollPeriod);

      this.logger.error(`Payroll processing failed for tenant ${tenantId}: ${(error as Error).message}`);
      throw error;
    }
  }

  async approvePayroll(tenantId: string, payrollPeriodId: string, approverId: string): Promise<PayrollPeriod> {
    this.logger.log(`Approving payroll period ${payrollPeriodId} for tenant ${tenantId}`);

    const payrollPeriod = await this.payrollPeriodService.findOne(tenantId, payrollPeriodId);

    if (payrollPeriod.status !== PayrollRunStatus.CALCULATED) {
      throw new BadRequestException('Payroll period must be calculated before approval');
    }

    // Validate all payslips are ready for approval
    const payslips = await this.payslipRepository.find({
      where: {
        tenantId,
        payrollPeriodId,
        status: In([PayrollStatus.PENDING, PayrollStatus.APPROVED]),
      },
    });

    const pendingPayslips = payslips.filter(p => p.status === PayrollStatus.PENDING);
    if (pendingPayslips.length === 0) {
      throw new BadRequestException('No payslips available for approval');
    }

    // Update payroll period
    payrollPeriod.status = PayrollRunStatus.APPROVED;
    payrollPeriod.approvedAt = new Date();
    payrollPeriod.approverId = approverId;

    // Update all pending payslips to approved
    await this.payslipRepository.update(
      {
        tenantId,
        payrollPeriodId,
        status: PayrollStatus.PENDING,
      },
      {
        status: PayrollStatus.APPROVED,
        approvedAt: new Date(),
        approverId,
      }
    );

    const updated = await this.payrollPeriodRepository.save(payrollPeriod);

    // Emit approval event
    this.eventEmitter.emit('payroll.approved', {
      tenantId,
      payrollPeriodId,
      approverId,
      payslipCount: pendingPayslips.length,
    });

    this.logger.log(`Approved payroll period ${payrollPeriodId} with ${pendingPayslips.length} payslips`);

    return updated;
  }

  async rejectPayroll(tenantId: string, payrollPeriodId: string, rejectionReason: string): Promise<PayrollPeriod> {
    this.logger.log(`Rejecting payroll period ${payrollPeriodId} for tenant ${tenantId}`);

    const payrollPeriod = await this.payrollPeriodService.findOne(tenantId, payrollPeriodId);

    if (![PayrollRunStatus.CALCULATED, PayrollRunStatus.APPROVED].includes(payrollPeriod.status)) {
      throw new BadRequestException('Payroll period cannot be rejected in current status');
    }

    // Update payroll period
    payrollPeriod.status = PayrollRunStatus.REJECTED;
    payrollPeriod.rejectedAt = new Date();
    payrollPeriod.rejectionReason = rejectionReason;

    // Update all approved payslips back to pending
    await this.payslipRepository.update(
      {
        tenantId,
        payrollPeriodId,
        status: PayrollStatus.APPROVED,
      },
      {
        status: PayrollStatus.PENDING,
        approvedAt: undefined,
        approverId: undefined,
      }
    );

    const updated = await this.payrollPeriodRepository.save(payrollPeriod);

    // Emit rejection event
    this.eventEmitter.emit('payroll.rejected', {
      tenantId,
      payrollPeriodId,
      rejectionReason,
    });

    this.logger.log(`Rejected payroll period ${payrollPeriodId}: ${rejectionReason}`);

    return updated;
  }

  async reversePayroll(tenantId: string, payrollPeriodId: string, reversalReason: string): Promise<PayrollPeriod> {
    this.logger.log(`Reversing payroll period ${payrollPeriodId} for tenant ${tenantId}`);

    const payrollPeriod = await this.payrollPeriodService.findOne(tenantId, payrollPeriodId);

    if (![PayrollRunStatus.PROCESSED, PayrollRunStatus.PARTIALLY_PROCESSED].includes(payrollPeriod.status)) {
      throw new BadRequestException('Only processed payroll periods can be reversed');
    }

    // Check if reversal is allowed (e.g., within certain time frame)
    const daysSinceProcessing = Math.floor(
      (new Date().getTime() - (payrollPeriod.processingCompletedAt?.getTime() || 0)) / (1000 * 60 * 60 * 24)
    );

    if (daysSinceProcessing > 30) {
      throw new BadRequestException('Payroll reversal not allowed after 30 days');
    }

    // Update payroll period
    payrollPeriod.status = PayrollRunStatus.REVERSED;
    payrollPeriod.reversedAt = new Date();
    payrollPeriod.reversalReason = reversalReason;

    // Update all processed payslips
    await this.payslipRepository.update(
      {
        tenantId,
        payrollPeriodId,
        status: In([PayrollStatus.PROCESSED, PayrollStatus.PAID]),
      },
      {
        status: PayrollStatus.REVERSED,
        reversedAt: new Date(),
        reversalReason,
      }
    );

    const updated = await this.payrollPeriodRepository.save(payrollPeriod);

    // Emit reversal event
    this.eventEmitter.emit('payroll.reversed', {
      tenantId,
      payrollPeriodId,
      reversalReason,
    });

    this.logger.log(`Reversed payroll period ${payrollPeriodId}: ${reversalReason}`);

    return updated;
  }

  private async getPayslipsToProcess(tenantId: string, processPayrollDto: ProcessPayrollDto): Promise<Payslip[]> {
    const where: any = {
      tenantId,
      payrollPeriodId: processPayrollDto.payrollPeriodId,
      status: PayrollStatus.APPROVED,
    };

    // Filter by specific payslip IDs if provided
    if (processPayrollDto.payslipIds && processPayrollDto.payslipIds.length > 0) {
      where.id = In(processPayrollDto.payslipIds);
    }

    return this.payslipRepository.find({
      where,
      relations: ['employee', 'payrollPeriod'],
    });
  }

  private async validatePayslipsForProcessing(payslips: Payslip[]): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const result = {
      isValid: true,
      errors: [] as string[],
      warnings: [] as string[],
    };

    for (const payslip of payslips) {
      // Check if payslip has valid net pay
      if (payslip.netPay <= 0) {
        result.errors.push(`Employee ${payslip.employee.firstName} ${payslip.employee.lastName}: Net pay must be greater than zero`);
        result.isValid = false;
      }

      // Check if employee has valid payment information
      if (payslip.paymentMethod === PaymentMethod.DIRECT_DEPOSIT) {
        if (!payslip.employee.bankingInfo) {
          result.errors.push(`Employee ${payslip.employee.firstName} ${payslip.employee.lastName}: Missing banking information for direct deposit`);
          result.isValid = false;
        }
      }

      // Check for duplicate processing
      if (payslip.paymentProcessedAt) {
        result.warnings.push(`Employee ${payslip.employee.firstName} ${payslip.employee.lastName}: Payslip already processed`);
      }
    }

    return result;
  }

  private async generatePaymentInstructions(payslips: Payslip[]): Promise<PaymentInstruction[]> {
    const instructions: PaymentInstruction[] = [];

    for (const payslip of payslips) {
      const instruction: PaymentInstruction = {
        employeeId: payslip.employeeId,
        employeeName: `${payslip.employee.firstName} ${payslip.employee.lastName}`,
        amount: payslip.netPay,
        currency: payslip.currency,
        paymentMethod: payslip.paymentMethod,
        payslipId: payslip.id,
        payrollPeriodId: payslip.payrollPeriodId,
      };

      // Add banking information for direct deposit
      if (payslip.paymentMethod === PaymentMethod.DIRECT_DEPOSIT && payslip.employee.bankingInfo) {
        instruction.bankAccount = {
          routingNumber: payslip.employee.bankingInfo.routingNumber || '',
          accountNumber: payslip.employee.bankingInfo.accountNumber || '',
          accountType: payslip.employee.bankingInfo.accountType || '',
        };
      }

      // Add address for check payments
      if (payslip.paymentMethod === PaymentMethod.CHECK) {
        const address = payslip.employee.addresses?.find(a => a.type === 'home') || payslip.employee.addresses?.[0];
        if (address) {
          instruction.address = {
            street: address.addressLine1,
            city: address.city,
            state: address.state || '',
            zipCode: address.postalCode,
            country: address.country,
          };
        }
      }

      instructions.push(instruction);
    }

    return instructions;
  }

  private async processPayments(
    instructions: PaymentInstruction[],
    processPayrollDto: ProcessPayrollDto
  ): Promise<PaymentResult[]> {
    const results: PaymentResult[] = [];

    for (const instruction of instructions) {
      try {
        this.logger.log(`Processing payment for employee ${instruction.employeeId}`);

        // Simulate payment processing
        const paymentResult = await this.processIndividualPayment(instruction, processPayrollDto);
        results.push(paymentResult);

      } catch (error) {
        this.logger.error(`Payment failed for employee ${instruction.employeeId}: ${(error as Error).message}`);

        results.push({
          employeeId: instruction.employeeId,
          payslipId: instruction.payslipId,
          success: false,
          errorMessage: (error as Error).message,
          processedAt: new Date(),
        });
      }
    }

    return results;
  }

  private async processIndividualPayment(
    instruction: PaymentInstruction,
    processPayrollDto: ProcessPayrollDto
  ): Promise<PaymentResult> {
    // This is a simplified payment processing simulation
    // In a real implementation, this would integrate with banking APIs, payment processors, etc.

    const result: PaymentResult = {
      employeeId: instruction.employeeId,
      payslipId: instruction.payslipId,
      success: false,
      processedAt: new Date(),
    };

    try {
      switch (instruction.paymentMethod) {
        case PaymentMethod.DIRECT_DEPOSIT:
          result.transactionId = await this.processDirectDeposit(instruction);
          break;
        case PaymentMethod.CHECK:
          result.transactionId = await this.processCheck(instruction);
          break;
        case PaymentMethod.CASH:
          result.transactionId = await this.processCash(instruction);
          break;
        default:
          throw new Error(`Unsupported payment method: ${instruction.paymentMethod}`);
      }

      result.success = true;
      this.logger.log(`Payment successful for employee ${instruction.employeeId}: ${result.transactionId}`);

    } catch (error) {
      result.errorMessage = (error as Error).message;
      this.logger.error(`Payment failed for employee ${instruction.employeeId}: ${(error as Error).message}`);
    }

    return result;
  }

  private async processDirectDeposit(instruction: PaymentInstruction): Promise<string> {
    // Simulate direct deposit processing
    if (!instruction.bankAccount) {
      throw new Error('Banking information required for direct deposit');
    }

    // Validate bank account information
    if (!instruction.bankAccount.routingNumber || !instruction.bankAccount.accountNumber) {
      throw new Error('Invalid banking information');
    }

    // Generate transaction ID
    const transactionId = `DD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 100));

    return transactionId;
  }

  private async processCheck(instruction: PaymentInstruction): Promise<string> {
    // Simulate check processing
    if (!instruction.address) {
      throw new Error('Address required for check payment');
    }

    // Generate check number
    const checkNumber = `CHK-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 50));

    return checkNumber;
  }

  private async processCash(instruction: PaymentInstruction): Promise<string> {
    // Simulate cash processing
    const receiptNumber = `CASH-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;

    return receiptNumber;
  }

  private async updatePayslipsWithResults(payslips: Payslip[], paymentResults: PaymentResult[]): Promise<void> {
    for (const payslip of payslips) {
      const result = paymentResults.find(r => r.payslipId === payslip.id);
      if (!result) continue;

      if (result.success) {
        payslip.status = PayrollStatus.PROCESSED;
        payslip.paymentProcessedAt = result.processedAt;
        payslip.paymentReference = result.transactionId;
      } else {
        payslip.status = PayrollStatus.FAILED;
        payslip.processingIssues = {
          ...payslip.processingIssues,
          errors: [
            ...(payslip.processingIssues?.errors || []),
            {
              code: 'PAYMENT_FAILED',
              message: result.errorMessage || 'Payment processing failed',
              field: 'payment',
              severity: 'error' as const,
              timestamp: result.processedAt,
            },
          ],
          warnings: payslip.processingIssues?.warnings || [],
        };
      }

      await this.payslipRepository.save(payslip);
    }
  }

  private async sendPayrollNotifications(payslips: Payslip[], paymentResults: PaymentResult[]): Promise<void> {
    for (const payslip of payslips) {
      const result = paymentResults.find(r => r.payslipId === payslip.id);
      if (!result) continue;

      // Emit notification event
      this.eventEmitter.emit('payroll.notification', {
        type: result.success ? 'payment_processed' : 'payment_failed',
        employeeId: payslip.employeeId,
        payslipId: payslip.id,
        amount: payslip.netPay,
        transactionId: result.transactionId,
        errorMessage: result.errorMessage,
      });
    }
  }
}
