import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { ServiceDiscoveryService, ServiceInstance } from './service-discovery.service';

export interface ProxyRequest {
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  query?: Record<string, string>;
  params?: Record<string, string>;
}

export interface ProxyResponse {
  status: number;
  headers: Record<string, string>;
  data: any;
  duration: number;
}

export interface RoutingRule {
  path: string;
  service: string;
  rewrite?: string;
  methods?: string[];
  auth?: boolean;
  rateLimit?: {
    windowMs: number;
    max: number;
  };
  timeout?: number;
  retries?: number;
}

@Injectable()
export class ApiGatewayService {
  private readonly logger = new Logger(ApiGatewayService.name);
  private readonly defaultTimeout: number;
  private readonly defaultRetries: number;
  private readonly routingRules: RoutingRule[];

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly serviceDiscovery: ServiceDiscoveryService,
  ) {
    this.defaultTimeout = this.configService.get<number>('GATEWAY_DEFAULT_TIMEOUT', 30000);
    this.defaultRetries = this.configService.get<number>('GATEWAY_DEFAULT_RETRIES', 3);
    this.routingRules = this.loadRoutingRules();
  }

  /**
   * Load routing rules from configuration
   */
  private loadRoutingRules(): RoutingRule[] {
    return [
      {
        path: '/api/v1/auth/*',
        service: 'auth-service',
        rewrite: '/api/v1/auth/*',
        auth: false,
        timeout: 30000,
      },
      {
        path: '/api/v1/employees/*',
        service: 'employee-service',
        rewrite: '/employees/*',
        auth: true,
        rateLimit: { windowMs: 60000, max: 100 },
      },
      {
        path: '/api/v1/payroll/*',
        service: 'payroll-service',
        rewrite: '/payroll/*',
        auth: true,
        rateLimit: { windowMs: 60000, max: 50 },
      },
      {
        path: '/api/v1/performance/*',
        service: 'performance-service',
        rewrite: '/performance/*',
        auth: true,
        rateLimit: { windowMs: 60000, max: 100 },
      },
      {
        path: '/api/v1/ai/*',
        service: 'ai-service',
        rewrite: '/api/v1/*',
        auth: true,
        rateLimit: { windowMs: 60000, max: 20 },
        timeout: 60000, // AI operations may take longer
      },
      {
        path: '/api/v1/notifications/*',
        service: 'notification-service',
        rewrite: '/notifications/*',
        auth: true,
        rateLimit: { windowMs: 60000, max: 200 },
      },
    ];
  }

  /**
   * Find routing rule for a given path
   */
  findRoutingRule(path: string): RoutingRule | null {
    return this.routingRules.find(rule => {
      const pattern = rule.path.replace(/\*/g, '.*');
      const regex = new RegExp(`^${pattern}$`);
      return regex.test(path);
    }) || null;
  }

  /**
   * Proxy request to appropriate microservice
   */
  async proxyRequest(request: ProxyRequest): Promise<ProxyResponse> {
    const startTime = Date.now();
    
    try {
      // Find routing rule
      const rule = this.findRoutingRule(request.url);
      if (!rule) {
        throw new HttpException(
          `No routing rule found for path: ${request.url}`,
          HttpStatus.NOT_FOUND,
        );
      }

      // Get service instance
      const instance = this.serviceDiscovery.getInstance(rule.service);
      if (!instance) {
        throw new HttpException(
          `No healthy instances available for service: ${rule.service}`,
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      // Build target URL
      const targetUrl = this.buildTargetUrl(instance, request.url, rule);
      
      // Prepare headers
      const headers = this.prepareHeaders(request.headers, instance);

      // Execute request with retries
      const response = await this.executeWithRetries(
        {
          method: request.method,
          url: targetUrl,
          headers,
          data: request.body,
          params: request.query,
          timeout: rule.timeout || this.defaultTimeout,
        },
        rule.retries || this.defaultRetries,
      );

      const duration = Date.now() - startTime;

      this.logger.debug(
        `Proxied ${request.method} ${request.url} -> ${targetUrl} (${response.status}) in ${duration}ms`,
      );

      return {
        status: response.status,
        headers: response.headers,
        data: response.data,
        duration,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.error(
        `Failed to proxy ${request.method} ${request.url}: ${(error as Error).message} (${duration}ms)`,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      // Handle different types of errors
      if ((error as any).code === 'ECONNREFUSED' || (error as any).code === 'ENOTFOUND') {
        throw new HttpException(
          'Service temporarily unavailable',
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      if ((error as any).code === 'ETIMEDOUT') {
        throw new HttpException(
          'Request timeout',
          HttpStatus.REQUEST_TIMEOUT,
        );
      }

      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Build target URL for the service
   */
  private buildTargetUrl(instance: ServiceInstance, originalUrl: string, rule: RoutingRule): string {
    const baseUrl = `${instance.protocol}://${instance.host}:${instance.port}`;
    
    if (rule.rewrite) {
      // Apply rewrite rule
      const pattern = rule.path.replace(/\*/g, '(.*)');
      const regex = new RegExp(`^${pattern}$`);
      const match = originalUrl.match(regex);
      
      if (match && match[1]) {
        const rewrittenPath = rule.rewrite.replace(/\*/g, match[1]);
        return `${baseUrl}${rewrittenPath}`;
      }
    }

    // Default: use original path
    return `${baseUrl}${originalUrl}`;
  }

  /**
   * Prepare headers for the proxied request
   */
  private prepareHeaders(originalHeaders: Record<string, string>, instance: ServiceInstance): Record<string, string> {
    const headers = { ...originalHeaders };

    // Remove hop-by-hop headers
    delete headers['connection'];
    delete headers['keep-alive'];
    delete headers['proxy-authenticate'];
    delete headers['proxy-authorization'];
    delete headers['te'];
    delete headers['trailers'];
    delete headers['transfer-encoding'];
    delete headers['upgrade'];

    // Add service-specific headers
    headers['X-Forwarded-By'] = 'PeopleNest-Gateway';
    headers['X-Service-Instance'] = instance.id;
    headers['X-Service-Version'] = instance.version;

    // Add correlation ID if not present
    if (!headers['X-Correlation-ID']) {
      headers['X-Correlation-ID'] = this.generateCorrelationId();
    }

    return headers;
  }

  /**
   * Execute request with retry logic
   */
  private async executeWithRetries(requestConfig: any, maxRetries: number): Promise<any> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await firstValueFrom(this.httpService.request(requestConfig));
      } catch (error) {
        lastError = error;
        
        // Don't retry on client errors (4xx)
        if ((error as any).response && (error as any).response.status >= 400 && (error as any).response.status < 500) {
          throw error;
        }

        if (attempt < maxRetries) {
          const delay = this.calculateRetryDelay(attempt);
          this.logger.warn(
            `Request failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms: ${(error as Error).message}`,
          );
          await this.sleep(delay);
        }
      }
    }

    throw lastError;
  }

  /**
   * Calculate exponential backoff delay
   */
  private calculateRetryDelay(attempt: number): number {
    const baseDelay = 1000; // 1 second
    const maxDelay = 10000; // 10 seconds
    const delay = baseDelay * Math.pow(2, attempt - 1);
    return Math.min(delay, maxDelay);
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate correlation ID for request tracing
   */
  private generateCorrelationId(): string {
    return `gw-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get gateway statistics
   */
  getStatistics(): {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    serviceBreakdown: Record<string, { requests: number; errors: number; avgResponseTime: number }>;
  } {
    // This would be implemented with proper metrics collection
    // For now, return mock data
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      serviceBreakdown: {},
    };
  }

  /**
   * Get routing configuration
   */
  getRoutingConfiguration(): RoutingRule[] {
    return [...this.routingRules];
  }

  /**
   * Update routing rule
   */
  updateRoutingRule(path: string, updates: Partial<RoutingRule>): void {
    const index = this.routingRules.findIndex(rule => rule.path === path);
    if (index >= 0) {
      this.routingRules[index] = { ...this.routingRules[index], ...updates };
      this.logger.log(`Updated routing rule for path: ${path}`);
    }
  }

  /**
   * Add new routing rule
   */
  addRoutingRule(rule: RoutingRule): void {
    this.routingRules.push(rule);
    this.logger.log(`Added new routing rule for path: ${rule.path}`);
  }

  /**
   * Remove routing rule
   */
  removeRoutingRule(path: string): void {
    const index = this.routingRules.findIndex(rule => rule.path === path);
    if (index >= 0) {
      this.routingRules.splice(index, 1);
      this.logger.log(`Removed routing rule for path: ${path}`);
    }
  }
}
