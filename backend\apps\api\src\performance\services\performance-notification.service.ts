import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Notification, NotificationType, NotificationCategory } from '@app/database/entities/notification.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { PerformanceReview, ReviewStatus } from '@app/database/entities/performance-review.entity';
import { Goal, GoalStatus } from '@app/database/entities/goal.entity';
import { Feedback, FeedbackStatus } from '@app/database/entities/feedback.entity';
import { TenantService } from '../../tenant/tenant.service';

@Injectable()
export class PerformanceNotificationService {
  constructor(
    @InjectRepository(Notification)
    private notificationRepository: Repository<Notification>,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
    @InjectRepository(PerformanceReview)
    private performanceReviewRepository: Repository<PerformanceReview>,
    @InjectRepository(Goal)
    private goalRepository: Repository<Goal>,
    private tenantService: TenantService,
  ) {}

  @OnEvent('performance-review.created')
  async handlePerformanceReviewCreated(payload: {
    performanceReview: PerformanceReview;
    tenantId: string;
    userId: string;
  }) {
    const { performanceReview, tenantId, userId } = payload;

    // Notify employee about new performance review
    await this.createNotification({
      tenantId,
      recipientId: performanceReview.employeeId,
      title: 'New Performance Review Created',
      message: `A new ${performanceReview.reviewType} performance review has been created for the period ${performanceReview.reviewPeriodStart.toDateString()} to ${performanceReview.reviewPeriodEnd.toDateString()}.`,
      notificationType: NotificationType.INFO,
      notificationCategory: NotificationCategory.PERFORMANCE,
      metadata: {
        entityType: 'performance-review',
        entityId: performanceReview.id,
        actionUrl: `/performance/reviews/${performanceReview.id}`,
        priority: 'medium',
      },
      createdBy: userId,
    });

    // Notify manager if different from creator
    if (performanceReview.reviewerId && performanceReview.reviewerId !== userId) {
      await this.createNotification({
        tenantId,
        recipientId: performanceReview.reviewerId,
        title: 'Performance Review Assignment',
        message: `You have been assigned as a reviewer for ${performanceReview.employee?.firstName} ${performanceReview.employee?.lastName}'s performance review.`,
        notificationType: NotificationType.INFO,
        notificationCategory: NotificationCategory.PERFORMANCE,
        metadata: {
          entityType: 'performance-review',
          entityId: performanceReview.id,
          actionUrl: `/performance/reviews/${performanceReview.id}`,
          priority: 'medium',
        },
        createdBy: userId,
      });
    }
  }

  @OnEvent('performance-review.updated')
  async handlePerformanceReviewUpdated(payload: {
    performanceReview: PerformanceReview;
    tenantId: string;
    userId: string;
    changes: any;
  }) {
    const { performanceReview, tenantId, userId, changes } = payload;

    // Handle status change notifications
    if (changes.status) {
      await this.handleReviewStatusChange(performanceReview, tenantId, userId, changes.status);
    }
  }

  @OnEvent('goal.created')
  async handleGoalCreated(payload: {
    goal: Goal;
    tenantId: string;
    userId: string;
  }) {
    const { goal, tenantId, userId } = payload;

    // Notify employee about new goal
    if (goal.employeeId !== userId) {
      await this.createNotification({
        tenantId,
        recipientId: goal.employeeId,
        title: 'New Goal Assigned',
        message: `A new ${goal.goalType} goal "${goal.title}" has been assigned to you with target date ${goal.targetDate.toDateString()}.`,
        notificationType: NotificationType.INFO,
        notificationCategory: NotificationCategory.PERFORMANCE,
        metadata: {
          entityType: 'goal',
          entityId: goal.id,
          actionUrl: `/performance/goals/${goal.id}`,
          priority: goal.priority === 'high' ? 'high' : 'medium',
        },
        createdBy: userId,
      });
    }

    // Notify manager if different from creator
    if (goal.managerId && goal.managerId !== userId) {
      await this.createNotification({
        tenantId,
        recipientId: goal.managerId,
        title: 'Goal Created for Team Member',
        message: `A new goal "${goal.title}" has been created for ${goal.employee?.firstName} ${goal.employee?.lastName}.`,
        notificationType: NotificationType.INFO,
        notificationCategory: NotificationCategory.PERFORMANCE,
        metadata: {
          entityType: 'goal',
          entityId: goal.id,
          actionUrl: `/performance/goals/${goal.id}`,
          priority: 'low',
        },
        createdBy: userId,
      });
    }
  }

  @OnEvent('goal.updated')
  async handleGoalUpdated(payload: {
    goal: Goal;
    tenantId: string;
    userId: string;
    changes: any;
  }) {
    const { goal, tenantId, userId, changes } = payload;

    // Handle status change notifications
    if (changes.status === GoalStatus.ACHIEVED) {
      await this.createNotification({
        tenantId,
        recipientId: goal.employeeId,
        title: 'Goal Achieved! 🎉',
        message: `Congratulations! You have successfully achieved your goal "${goal.title}".`,
        notificationType: NotificationType.SUCCESS,
        notificationCategory: NotificationCategory.PERFORMANCE,
        metadata: {
          entityType: 'goal',
          entityId: goal.id,
          actionUrl: `/performance/goals/${goal.id}`,
          priority: 'high',
        },
        createdBy: userId,
      });

      // Notify manager
      if (goal.managerId) {
        await this.createNotification({
          tenantId,
          recipientId: goal.managerId,
          title: 'Team Member Goal Achieved',
          message: `${goal.employee?.firstName} ${goal.employee?.lastName} has achieved their goal "${goal.title}".`,
          notificationType: NotificationType.SUCCESS,
          notificationCategory: NotificationCategory.PERFORMANCE,
          metadata: {
            entityType: 'goal',
            entityId: goal.id,
            actionUrl: `/performance/goals/${goal.id}`,
            priority: 'medium',
          },
          createdBy: userId,
        });
      }
    }
  }

  @OnEvent('feedback.created')
  async handleFeedbackCreated(payload: {
    feedback: Feedback;
    tenantId: string;
    userId: string;
  }) {
    const { feedback, tenantId, userId } = payload;

    // Notify recipient about new feedback request
    await this.createNotification({
      tenantId,
      recipientId: feedback.recipientId,
      title: 'New Feedback Request',
      message: `You have received a new ${feedback.feedbackType} feedback request: "${feedback.subject}".`,
      notificationType: NotificationType.INFO,
      notificationCategory: NotificationCategory.PERFORMANCE,
      metadata: {
        entityType: 'feedback',
        entityId: feedback.id,
        actionUrl: `/performance/feedback/${feedback.id}`,
        priority: 'medium',
      },
      createdBy: userId,
    });
  }

  @OnEvent('feedback.updated')
  async handleFeedbackUpdated(payload: {
    feedback: Feedback;
    tenantId: string;
    userId: string;
    changes: any;
  }) {
    const { feedback, tenantId, userId, changes } = payload;

    // Handle feedback submission
    if (changes.status === FeedbackStatus.SUBMITTED) {
      await this.createNotification({
        tenantId,
        recipientId: feedback.giverId,
        title: 'Feedback Submitted',
        message: `Your feedback for ${feedback.recipient?.firstName} ${feedback.recipient?.lastName} has been submitted successfully.`,
        notificationType: NotificationType.SUCCESS,
        notificationCategory: NotificationCategory.PERFORMANCE,
        metadata: {
          entityType: 'feedback',
          entityId: feedback.id,
          actionUrl: `/performance/feedback/${feedback.id}`,
          priority: 'low',
        },
        createdBy: userId,
      });
    }

    // Handle feedback acknowledgment
    if (changes.status === FeedbackStatus.ACKNOWLEDGED) {
      await this.createNotification({
        tenantId,
        recipientId: feedback.giverId,
        title: 'Feedback Acknowledged',
        message: `${feedback.recipient?.firstName} ${feedback.recipient?.lastName} has acknowledged your feedback.`,
        notificationType: NotificationType.INFO,
        notificationCategory: NotificationCategory.PERFORMANCE,
        metadata: {
          entityType: 'feedback',
          entityId: feedback.id,
          actionUrl: `/performance/feedback/${feedback.id}`,
          priority: 'low',
        },
        createdBy: userId,
      });
    }
  }

  async sendGoalDeadlineReminders(tenantId: string): Promise<void> {
    const reminderDate = new Date();
    reminderDate.setDate(reminderDate.getDate() + 7); // 7 days before deadline

    // Find goals approaching deadline
    const approachingGoals = await this.goalRepository.find({
      where: {
        tenantId,
        status: GoalStatus.IN_PROGRESS,
        targetDate: reminderDate,
      },
      relations: ['employee', 'manager'],
    });

    for (const goal of approachingGoals) {
      // Notify employee
      await this.createNotification({
        tenantId,
        recipientId: goal.employeeId,
        title: 'Goal Deadline Approaching',
        message: `Your goal "${goal.title}" is due in 7 days (${goal.targetDate.toDateString()}). Current progress: ${goal.progressPercentage}%.`,
        notificationType: NotificationType.REMINDER,
        notificationCategory: NotificationCategory.PERFORMANCE,
        metadata: {
          entityType: 'goal',
          entityId: goal.id,
          actionUrl: `/performance/goals/${goal.id}`,
          priority: 'medium',
        },
        createdBy: 'system',
      });

      // Notify manager
      if (goal.managerId) {
        await this.createNotification({
          tenantId,
          recipientId: goal.managerId,
          title: 'Team Member Goal Deadline Approaching',
          message: `${goal.employee?.firstName} ${goal.employee?.lastName}'s goal "${goal.title}" is due in 7 days with ${goal.progressPercentage}% progress.`,
          notificationType: NotificationType.REMINDER,
          notificationCategory: NotificationCategory.PERFORMANCE,
          metadata: {
            entityType: 'goal',
            entityId: goal.id,
            actionUrl: `/performance/goals/${goal.id}`,
            priority: 'low',
          },
          createdBy: 'system',
        });
      }
    }
  }

  async sendReviewDueReminders(tenantId: string): Promise<void> {
    const reminderDate = new Date();
    reminderDate.setDate(reminderDate.getDate() + 3); // 3 days before due date

    // Find reviews approaching due date
    const dueReviews = await this.performanceReviewRepository.find({
      where: {
        tenantId,
        status: ReviewStatus.PENDING_SELF_ASSESSMENT,
        dueDate: reminderDate,
      },
      relations: ['employee', 'reviewer'],
    });

    for (const review of dueReviews) {
      await this.createNotification({
        tenantId,
        recipientId: review.employeeId,
        title: 'Performance Review Due Soon',
        message: `Your ${review.reviewType} performance review is due in 3 days (${review.dueDate?.toDateString()}). Please complete your self-assessment.`,
        notificationType: NotificationType.REMINDER,
        notificationCategory: NotificationCategory.PERFORMANCE,
        metadata: {
          entityType: 'performance-review',
          entityId: review.id,
          actionUrl: `/performance/reviews/${review.id}`,
          priority: 'high',
        },
        createdBy: 'system',
      });
    }
  }

  private async handleReviewStatusChange(
    review: PerformanceReview,
    tenantId: string,
    userId: string,
    newStatus: ReviewStatus,
  ): Promise<void> {
    switch (newStatus) {
      case ReviewStatus.PENDING_SELF_ASSESSMENT:
        await this.createNotification({
          tenantId,
          recipientId: review.employeeId,
          title: 'Self-Assessment Required',
          message: `Please complete your self-assessment for the ${review.reviewType} performance review.`,
          notificationType: NotificationType.REMINDER,
          notificationCategory: NotificationCategory.PERFORMANCE,
          metadata: {
            entityType: 'performance-review',
            entityId: review.id,
            actionUrl: `/performance/reviews/${review.id}`,
            priority: 'high',
          },
          createdBy: userId,
        });
        break;

      case ReviewStatus.PENDING_MANAGER_REVIEW:
        if (review.reviewerId) {
          await this.createNotification({
            tenantId,
            recipientId: review.reviewerId,
            title: 'Manager Review Required',
            message: `${review.employee?.firstName} ${review.employee?.lastName} has completed their self-assessment. Please complete your manager review.`,
            notificationType: NotificationType.REMINDER,
            notificationCategory: NotificationCategory.PERFORMANCE,
            metadata: {
              entityType: 'performance-review',
              entityId: review.id,
              actionUrl: `/performance/reviews/${review.id}`,
              priority: 'high',
            },
            createdBy: userId,
          });
        }
        break;

      case ReviewStatus.COMPLETED:
        await this.createNotification({
          tenantId,
          recipientId: review.employeeId,
          title: 'Performance Review Completed',
          message: `Your ${review.reviewType} performance review has been completed. You can now view your results and development plan.`,
          notificationType: NotificationType.SUCCESS,
          notificationCategory: NotificationCategory.PERFORMANCE,
          metadata: {
            entityType: 'performance-review',
            entityId: review.id,
            actionUrl: `/performance/reviews/${review.id}`,
            priority: 'medium',
          },
          createdBy: userId,
        });
        break;
    }
  }

  private async createNotification(notificationData: {
    tenantId: string;
    recipientId: string;
    title: string;
    message: string;
    notificationType: NotificationType;
    notificationCategory: NotificationCategory;
    metadata?: any;
    createdBy: string;
  }): Promise<Notification> {
    const notification = this.notificationRepository.create(notificationData);
    return this.notificationRepository.save(notification);
  }
}
