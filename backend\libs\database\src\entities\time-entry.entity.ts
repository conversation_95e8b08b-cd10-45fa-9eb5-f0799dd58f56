import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { Employee } from './employee.entity';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';

export enum TimeEntryType {
  REGULAR = 'regular',
  OVERTIME = 'overtime',
  BREAK = 'break',
  LUNCH = 'lunch',
  MEETING = 'meeting',
  TRAINING = 'training',
  PROJECT = 'project',
  ADMIN = 'admin',
}

export enum TimeEntryStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  LOCKED = 'locked',
}

@Entity('time_entries')
@Index(['tenantId', 'employeeId'])
@Index(['tenantId', 'date'])
@Index(['tenantId', 'status'])
export class TimeEntry {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'employee_id' })
  employeeId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @Column({ type: 'date' })
  date: Date;

  @Column({ type: 'time', name: 'start_time' })
  startTime: string;

  @Column({ type: 'time', name: 'end_time', nullable: true })
  endTime: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, name: 'hours_worked' })
  hoursWorked: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'break_duration' })
  breakDuration?: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'total_hours' })
  totalHours?: number;

  @Column({
    type: 'enum',
    enum: TimeEntryType,
    default: TimeEntryType.REGULAR,
    name: 'entry_type',
  })
  entryType: TimeEntryType;

  @Column({
    type: 'enum',
    enum: TimeEntryStatus,
    default: TimeEntryStatus.DRAFT,
  })
  status: TimeEntryStatus;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ length: 255, nullable: true, name: 'project_code' })
  projectCode: string;

  @Column({ length: 255, nullable: true, name: 'task_code' })
  taskCode: string;

  @Column({ type: 'boolean', default: false, name: 'is_billable' })
  isBillable: boolean;

  @Column({ type: 'decimal', precision: 8, scale: 2, nullable: true, name: 'billable_rate' })
  billableRate: number;

  @Column({ name: 'approved_by', nullable: true })
  approvedBy: string;

  @ManyToOne(() => Employee, { nullable: true })
  @JoinColumn({ name: 'approved_by' })
  approver: Employee;

  @Column({ name: 'approved_at', type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ name: 'rejected_by', nullable: true })
  rejectedBy?: string;

  @Column({ name: 'rejected_at', type: 'timestamp', nullable: true })
  rejectedAt?: Date;

  @Column({ type: 'text', nullable: true, name: 'rejection_reason' })
  rejectionReason: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;
}
