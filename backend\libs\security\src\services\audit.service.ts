import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';

import { AuditLog, AuditCategory, AuditLevel } from '@app/database/entities/audit-log.entity';
import { AuditLogEntry } from '../interfaces/auth.interface';
import { AuditAction } from '@app/common/enums/status.enum';

@Injectable()
export class AuditService {
  constructor(
    @InjectRepository(AuditLog)
    private auditLogRepository: Repository<AuditLog>,
  ) {}

  /**
   * Log authentication attempt
   */
  async logAuthAttempt(data: {
    userId?: string;
    email?: string;
    ip?: string;
    userAgent?: string;
    success: boolean;
    error?: string;
    action?: string;
    timestamp: Date;
  }): Promise<void> {
    const auditLog = this.auditLogRepository.create({
      userId: data.userId,
      action: AuditAction.LOGIN,
      category: AuditCategory.AUTHENTICATION,
      level: data.success ? AuditLevel.INFO : AuditLevel.WARNING,
      ipAddress: data.ip,
      userAgent: data.userAgent,
      description: data.success ? 'Authentication successful' : 'Authentication failed',
      metadata: {
        type: 'auth_attempt',
        email: data.email,
        success: data.success,
        error: data.error,
        timestamp: data.timestamp,
      },
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log user action
   */
  async logUserAction(data: {
    userId: string;
    action: string;
    resource?: string;
    resourceId?: string;
    ip?: string;
    userAgent?: string;
    success: boolean;
    error?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    const auditLog = this.auditLogRepository.create({
      userId: data.userId,
      action: this.mapStringToAuditAction(data.action),
      category: AuditCategory.USER_MANAGEMENT,
      level: data.success ? AuditLevel.INFO : AuditLevel.ERROR,
      entityType: data.resource,
      entityId: data.resourceId,
      ipAddress: data.ip,
      userAgent: data.userAgent,
      description: data.success ? `${data.action} successful` : `${data.action} failed`,
      metadata: {
        ...data.metadata,
        success: data.success,
        error: data.error,
        timestamp: new Date(),
      },
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log system event
   */
  async logSystemEvent(data: {
    action: string;
    resource?: string;
    success: boolean;
    error?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    const auditLog = this.auditLogRepository.create({
      action: this.mapStringToAuditAction(data.action),
      category: AuditCategory.SYSTEM,
      level: data.success ? AuditLevel.INFO : AuditLevel.ERROR,
      entityType: data.resource,
      description: data.success ? `${data.action} successful` : `${data.action} failed`,
      metadata: {
        ...data.metadata,
        type: 'system_event',
        success: data.success,
        error: data.error,
        timestamp: new Date(),
      },
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log data access
   */
  async logDataAccess(data: {
    userId: string;
    resource: string;
    resourceId?: string;
    action: 'read' | 'create' | 'update' | 'delete';
    ip?: string;
    userAgent?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    const auditLog = this.auditLogRepository.create({
      userId: data.userId,
      action: this.mapStringToAuditAction(data.action),
      category: AuditCategory.DATA_ACCESS,
      level: AuditLevel.INFO,
      entityType: data.resource,
      entityId: data.resourceId,
      ipAddress: data.ip,
      userAgent: data.userAgent,
      description: `Data access: ${data.action} on ${data.resource}`,
      metadata: {
        ...data.metadata,
        type: 'data_access',
        operation: data.action,
        success: true,
        timestamp: new Date(),
      },
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log security event
   */
  async logSecurityEvent(data: {
    userId?: string;
    action: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    ip?: string;
    userAgent?: string;
    description: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    const auditLog = this.auditLogRepository.create({
      userId: data.userId,
      action: this.mapStringToAuditAction(data.action),
      category: AuditCategory.AUTHORIZATION,
      level: data.severity === 'critical' ? AuditLevel.CRITICAL :
             data.severity === 'high' ? AuditLevel.ERROR :
             data.severity === 'medium' ? AuditLevel.WARNING : AuditLevel.INFO,
      ipAddress: data.ip,
      userAgent: data.userAgent,
      description: data.description,
      metadata: {
        ...data.metadata,
        type: 'security_event',
        severity: data.severity,
        success: false, // Security events are typically failures or suspicious activities
        timestamp: new Date(),
      },
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Get audit logs for a user
   */
  async getUserAuditLogs(
    userId: string,
    limit: number = 100,
    offset: number = 0,
  ): Promise<AuditLogEntry[]> {
    const logs = await this.auditLogRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });

    return logs.map(this.mapToAuditLogEntry);
  }

  /**
   * Get audit logs by action
   */
  async getAuditLogsByAction(
    action: AuditAction,
    limit: number = 100,
    offset: number = 0,
  ): Promise<AuditLogEntry[]> {
    const logs = await this.auditLogRepository.find({
      where: { action },
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });

    return logs.map(this.mapToAuditLogEntry);
  }

  /**
   * Get audit logs by date range
   */
  async getAuditLogsByDateRange(
    startDate: Date,
    endDate: Date,
    limit: number = 100,
    offset: number = 0,
  ): Promise<AuditLogEntry[]> {
    const logs = await this.auditLogRepository.find({
      where: {
        createdAt: Between(startDate, endDate),
      },
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });

    return logs.map(this.mapToAuditLogEntry);
  }

  /**
   * Get failed authentication attempts
   */
  async getFailedAuthAttempts(
    timeWindow: number = 3600000, // 1 hour in milliseconds
    limit: number = 100,
  ): Promise<AuditLogEntry[]> {
    const since = new Date(Date.now() - timeWindow);
    
    const logs = await this.auditLogRepository.find({
      where: {
        action: AuditAction.LOGIN,
        createdAt: Between(since, new Date()),
      },
      order: { createdAt: 'DESC' },
      take: limit,
    });

    return logs.map(this.mapToAuditLogEntry);
  }

  /**
   * Get security events
   */
  async getSecurityEvents(
    severity?: 'low' | 'medium' | 'high' | 'critical',
    limit: number = 100,
    offset: number = 0,
  ): Promise<AuditLogEntry[]> {
    const queryBuilder = this.auditLogRepository
      .createQueryBuilder('audit')
      .where("audit.metadata->>'type' = :type", { type: 'security_event' });

    if (severity) {
      queryBuilder.andWhere("audit.metadata->>'severity' = :severity", { severity });
    }

    const logs = await queryBuilder
      .orderBy('audit.createdAt', 'DESC')
      .take(limit)
      .skip(offset)
      .getMany();

    return logs.map(this.mapToAuditLogEntry);
  }

  /**
   * Get audit statistics
   */
  async getAuditStats(
    startDate: Date,
    endDate: Date,
  ): Promise<{
    totalEvents: number;
    successfulEvents: number;
    failedEvents: number;
    authAttempts: number;
    failedAuthAttempts: number;
    securityEvents: number;
    uniqueUsers: number;
    uniqueIPs: number;
  }> {
    const [
      totalEvents,
      successfulEvents,
      failedEvents,
      authAttempts,
      failedAuthAttempts,
      securityEvents,
      uniqueUsers,
      uniqueIPs,
    ] = await Promise.all([
      this.auditLogRepository.count({
        where: { createdAt: Between(startDate, endDate) },
      }),
      this.auditLogRepository
        .createQueryBuilder('audit')
        .where('audit.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .andWhere("audit.metadata->>'success' = :success", { success: 'true' })
        .getCount(),
      this.auditLogRepository
        .createQueryBuilder('audit')
        .where('audit.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .andWhere("audit.metadata->>'success' = :success", { success: 'false' })
        .getCount(),
      this.auditLogRepository.count({
        where: {
          createdAt: Between(startDate, endDate),
          action: AuditAction.LOGIN,
        },
      }),
      this.auditLogRepository
        .createQueryBuilder('audit')
        .where('audit.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .andWhere('audit.action = :action', { action: 'authentication' })
        .andWhere("audit.metadata->>'success' = :success", { success: 'false' })
        .getCount(),
      this.auditLogRepository
        .createQueryBuilder('audit')
        .where("audit.metadata->>'type' = :type", { type: 'security_event' })
        .andWhere('audit.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .getCount(),
      this.auditLogRepository
        .createQueryBuilder('audit')
        .select('COUNT(DISTINCT audit.userId)', 'count')
        .where('audit.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .andWhere('audit.userId IS NOT NULL')
        .getRawOne()
        .then(result => parseInt(result.count, 10)),
      this.auditLogRepository
        .createQueryBuilder('audit')
        .select('COUNT(DISTINCT audit.ipAddress)', 'count')
        .where('audit.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .andWhere('audit.ipAddress IS NOT NULL')
        .getRawOne()
        .then(result => parseInt(result.count, 10)),
    ]);

    return {
      totalEvents,
      successfulEvents,
      failedEvents,
      authAttempts,
      failedAuthAttempts,
      securityEvents,
      uniqueUsers,
      uniqueIPs,
    };
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs(retentionDays: number = 90): Promise<void> {
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
    
    await this.auditLogRepository.delete({
      createdAt: Between(new Date(0), cutoffDate),
    });
  }

  /**
   * Map audit log entity to audit log entry
   */
  private mapToAuditLogEntry(log: AuditLog): AuditLogEntry {
    return {
      id: log.id,
      userId: log.userId,
      email: log.metadata?.email,
      action: log.action,
      ip: log.ipAddress || '',
      userAgent: log.userAgent || '',
      success: log.metadata?.success === true || log.metadata?.success === 'true',
      error: log.metadata?.error,
      metadata: log.metadata,
      timestamp: log.createdAt,
    };
  }

  private mapStringToAuditAction(action: string): AuditAction {
    const actionMap: Record<string, AuditAction> = {
      'create': AuditAction.CREATE,
      'read': AuditAction.READ,
      'update': AuditAction.UPDATE,
      'delete': AuditAction.DELETE,
      'login': AuditAction.LOGIN,
      'logout': AuditAction.LOGOUT,
      'export': AuditAction.EXPORT,
      'import': AuditAction.IMPORT,
      'approve': AuditAction.APPROVE,
      'reject': AuditAction.REJECT,
      'submit': AuditAction.SUBMIT,
      'cancel': AuditAction.CANCEL,
    };

    return actionMap[action.toLowerCase()] || AuditAction.READ;
  }
}
